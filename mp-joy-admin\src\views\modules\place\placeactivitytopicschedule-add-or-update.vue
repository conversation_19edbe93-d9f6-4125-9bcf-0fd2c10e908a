<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
    
    <el-form-item label="日程名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="日程名称"></el-input>
    </el-form-item>
      <el-form-item label="会议日期" prop="scheduleDate">
        <el-date-picker
          :picker-options="pickerOptions"
          v-model="dataForm.scheduleDate"
          style="width: 100%"
          @change="dateChange"
          :default-value="activityInfo.startTime"
          type="date"
          value-format="yyyy/MM/dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="开始时间" prop="startTimeOnly">
        <el-time-picker
          v-model="dataForm.startTimeOnly"
          style="width: 100%"
          @change="timeChange"
          value-format="HH:mm"
          format="HH:mm"
          placeholder="选择开始时间">
        </el-time-picker>
      </el-form-item>

      <el-form-item label="讲课时长(分钟)" prop="duration">
        <el-input-number
          v-model="dataForm.duration"
          :min="1"
          :max="1440"
          @change="durationChange"
          placeholder="请输入讲课时长"
          style="width: 100%">
        </el-input-number>
      </el-form-item>

      <el-form-item label="结束时间" prop="endTimeOnly">
        <el-time-picker
          v-model="dataForm.endTimeOnly"
          style="width: 100%"
          value-format="HH:mm"
          format="HH:mm"
          placeholder="自动计算结束时间"
          :disabled="true">
        </el-time-picker>
      </el-form-item>
      <el-form-item label="会议主题" prop="placeActivityTopicId">
        <el-select v-model="dataForm.placeActivityTopicId" placeholder="会议主题" filterable>
          <el-option
            v-for="item in placeTopicList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="讲者" prop="scheduleGuestIds">
        <el-select v-model="dataForm.scheduleGuestIds" multiple placeholder="讲者" filterable>
          <el-option
            v-for="item in guestList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="主持" prop="scheduleSpeakerIds">
        <el-select v-model="dataForm.scheduleSpeakerIds" multiple placeholder="主持" filterable>
          <el-option
            v-for="item in guestList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="讨论" prop="scheduleDiscussIds">
        <el-select v-model="dataForm.scheduleDiscussIds" multiple placeholder="讨论" filterable>
          <el-option
            v-for="item in guestList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
    <el-form-item label="讲者别名" prop="aliasGuestName">
      <el-input v-model="dataForm.aliasGuestName" placeholder="讲者别名"></el-input>
    </el-form-item>
    <el-form-item label="主持别名" prop="aliasSpeakerName">
      <el-input v-model="dataForm.aliasSpeakerName" placeholder="主持别名"></el-input>
    </el-form-item>
    <el-form-item label="讨论别名" prop="aliasDiscussName">
      <el-input v-model="dataForm.aliasDiscussName" placeholder="讨论别名"></el-input>
    </el-form-item>

    <!-- 时间同步选项 - 仅在编辑模式下显示 -->
    <el-form-item v-if="dataForm.id" label="时间调整">
      <el-checkbox v-model="dataForm.syncFollowingSchedules">
        修改时间后自动调整后续日程时间
      </el-checkbox>
      <div class="sync-hint">
        <i class="el-icon-info"></i>
        <span>勾选后，修改此日程的结束时间将自动调整后续所有日程的时间，保持时间连续性</span>
      </div>
    </el-form-item>

    <!-- 重置确认状态选项 - 仅在编辑模式下显示 -->
    <el-form-item v-if="dataForm.id" label="重置确认状态" prop="resetConfirmStatus">
      <el-switch
        v-model="dataForm.resetConfirmStatus"
        active-text="是"
        inactive-text="否">
      </el-switch>
      <div class="reset-hint">
        <i class="el-icon-warning"></i>
        <span>开启后将重置所有相关专家的确认状态，专家需要重新确认日程安排</span>
      </div>
    </el-form-item>

    <!-- <el-form-item label="排序，数值越小越靠前" prop="orderBy">
      <el-input v-model="dataForm.orderBy" placeholder="排序，数值越小越靠前"></el-input>
    </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
      placeTopicList: [],
      guestList: [],
      activityInfo: {},
      insertPosition: null, // 插入位置
        dataForm: {
          id: 0,
          activityId: '',
          orderBy: 0,
          placeActivityTopicId: '',
          name: '',
          startTime: '',
          endTime: '',
          aliasGuestName: '',
          aliasSpeakerName: '',
          aliasDiscussName: '',
          scheduleGuestIds: [],
          scheduleSpeakerIds: [],
          scheduleDiscussIds: [],
          times: [],
          videoUrl: '',
          scheduleDate: '',
          startTimeOnly: '',
          endTimeOnly: '',
          duration: 60,
          syncFollowingSchedules: true, // 默认开启时间同步
          resetConfirmStatus: false // 重置确认状态
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          scheduleDate: [
            { required: true, message: '会议日期不能为空', trigger: 'change' }
          ],
          startTimeOnly: [
            { required: true, message: '开始时间不能为空', trigger: 'change' }
          ],
          duration: [
            { required: true, message: '讲课时长不能为空', trigger: 'blur' },
            { type: 'number', min: 1, max: 1440, message: '讲课时长必须在1-1440分钟之间', trigger: 'blur' }
          ],
          orderBy: [
            { required: true, message: '排序，数值越小越靠前不能为空', trigger: 'blur' }
          ],
          placeActivityTopicId: [
            { required: true, message: '主题ID不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '日程名称不能为空', trigger: 'blur' }
          ],
          startTime: [
            { required: true, message: '开始时间不能为空', trigger: 'blur' }
          ],
          endTime: [
            { required: true, message: '结束时间不能为空', trigger: 'blur' }
          ],
        },
        pickerOptions:{
           disabledDate: (time) => {
              // 如果函数里处理的数据比较麻烦,也可以单独放在一个函数里,避免data数据太臃肿
              return this.dealDisabledDate(time);
            }
        },
      }
    },
    methods: {
      init (activityId,placeActivityTopicId,id, insertPosition, timeInfo) {
        this.dataForm.activityId = activityId;
        this.dataForm.placeActivityTopicId = placeActivityTopicId || undefined;
        this.dataForm.id = id || 0
        this.insertPosition = insertPosition || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()

          // 如果是新增日程且有时间信息，预填充时间字段
          if (!this.dataForm.id && timeInfo) {
            this.prefillTimeInfo(timeInfo)
          }

          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivitytopicschedule/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                const schedule = data.placeActivityTopicSchedule
                this.dataForm.activityId = schedule.activityId
                this.dataForm.placeId = schedule.placeId
                this.dataForm.orderBy = schedule.orderBy
                this.dataForm.placeActivityTopicId = schedule.placeActivityTopicId
                this.dataForm.name = schedule.name
                this.dataForm.scheduleGuestIds = schedule.scheduleGuestIds
                this.dataForm.scheduleSpeakerIds = schedule.scheduleSpeakerIds
                this.dataForm.scheduleDiscussIds = schedule.scheduleDiscussIds
                this.dataForm.startTime = schedule.startTime
                this.dataForm.endTime = schedule.endTime
                this.dataForm.videoUrl = schedule.videoUrl
                this.dataForm.aliasGuestName = schedule.aliasGuestName
                this.dataForm.aliasSpeakerName = schedule.aliasSpeakerName
                this.dataForm.aliasDiscussName = schedule.aliasDiscussName

                // 解析现有的时间数据到新的字段
                if (schedule.startTime) {
                  const startDate = new Date(schedule.startTime)
                  const endDate = new Date(schedule.endTime)

                  // 设置日期
                  this.dataForm.scheduleDate = this.formatDate(startDate)

                  // 设置开始时间
                  this.dataForm.startTimeOnly = this.formatTime(startDate)

                  // 设置结束时间
                  this.dataForm.endTimeOnly = this.formatTime(endDate)

                  // 计算时长（分钟）
                  this.dataForm.duration = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60))
                }

                // 保持原有的times字段用于兼容
                this.$set(this.dataForm, "times", [schedule.startTime, schedule.endTime])
              }
            })
          }
        })
        this.getPlaceTopic();
        this.getGuest();
        this.getActivity();
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivitytopicschedule/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'placeId': this.dataForm.placeId,
                'orderBy': this.dataForm.orderBy,
                'placeActivityTopicId': this.dataForm.placeActivityTopicId,
                'name': this.dataForm.name,
                'startTime': this.dataForm.startTime,
                'endTime': this.dataForm.endTime,
                'scheduleGuestIds': this.dataForm.scheduleGuestIds,
                'scheduleSpeakerIds': this.dataForm.scheduleSpeakerIds,
                'scheduleDiscussIds': this.dataForm.scheduleDiscussIds,
                'aliasGuestName': this.dataForm.aliasGuestName,
                'aliasSpeakerName': this.dataForm.aliasSpeakerName,
                'aliasDiscussName': this.dataForm.aliasDiscussName,
                'videoUrl': this.dataForm.videoUrl,
                'insertPosition': this.insertPosition, // 插入位置
                'syncFollowingSchedules': this.dataForm.syncFollowingSchedules, // 时间同步选项
                'resetConfirmStatus': this.dataForm.resetConfirmStatus // 重置确认状态
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
    getPlace() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivity/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.placeList = data.result;
        }
      });
    },
    getPlaceTopic() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivitytopic/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.placeTopicList = data.result;
        }
      });
    },
    getGuest() {
      this.$http({
        url: this.$http.adornUrl(
          `/activity/activityguest/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.guestList = data.result;
        }
      });
    },
      getActivity() {
          this.$http({
            url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.activityInfo = data.activity
            }
          })
      },
      dealDisabledDate (time) {
        // time 是一个new Date数据
        if(this.activityInfo.endTime != null && this.activityInfo.startTime != null){
          return (time.getTime() + 24*60*60*1000) < (new Date(this.activityInfo.startTime).getTime()) || time.getTime() >= (new Date(this.activityInfo.endTime).getTime()) ;//时间范围必须是时间戳
        }
      },
      dateChange() {
        this.updateDateTime()
      },
      timeChange() {
        this.updateDateTime()
      },
      durationChange() {
        this.updateDateTime()
      },
      // 更新完整的日期时间
      updateDateTime() {
        if (this.dataForm.scheduleDate && this.dataForm.startTimeOnly && this.dataForm.duration) {
          // 构建开始时间
          const startDateTime = `${this.dataForm.scheduleDate} ${this.dataForm.startTimeOnly}:00`
          this.dataForm.startTime = startDateTime

          // 计算结束时间
          const startDate = new Date(startDateTime)
          const endDate = new Date(startDate.getTime() + this.dataForm.duration * 60 * 1000)

          // 设置结束时间显示
          this.dataForm.endTimeOnly = this.formatTime(endDate)

          // 设置完整的结束时间
          this.dataForm.endTime = this.formatDateTime(endDate)

          // 更新times数组用于兼容
          this.$set(this.dataForm, "times", [this.dataForm.startTime, this.dataForm.endTime])
        }
      },
      // 格式化日期 yyyy/MM/dd
      formatDate(date) {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}/${month}/${day}`
      },
      // 格式化时间 HH:mm
      formatTime(date) {
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        return `${hours}:${minutes}`
      },
      // 格式化完整日期时间 yyyy/MM/dd HH:mm:ss
      formatDateTime(date) {
        const dateStr = this.formatDate(date)
        const timeStr = this.formatTime(date)
        return `${dateStr} ${timeStr}:00`
      },
      // 预填充时间信息
      prefillTimeInfo(timeInfo) {
        if (!timeInfo) return

        try {
          // 设置开始时间
          if (timeInfo.startTime) {
            const startDate = new Date(timeInfo.startTime)
            this.dataForm.scheduleDate = this.formatDate(startDate)
            this.dataForm.startTimeOnly = this.formatTime(startDate)
            this.dataForm.startTime = timeInfo.startTime
          }

          // 设置结束时间
          if (timeInfo.endTime) {
            const endDate = new Date(timeInfo.endTime)
            this.dataForm.endTimeOnly = this.formatTime(endDate)
            this.dataForm.endTime = timeInfo.endTime
          }

          // 设置时长
          if (timeInfo.duration && timeInfo.duration > 0) {
            this.dataForm.duration = timeInfo.duration
          }

          // 如果只有开始时间，根据时长计算结束时间
          if (timeInfo.startTime && !timeInfo.endTime && timeInfo.duration) {
            const startDate = new Date(timeInfo.startTime)
            const endDate = new Date(startDate.getTime() + timeInfo.duration * 60 * 1000)
            this.dataForm.endTimeOnly = this.formatTime(endDate)
            this.dataForm.endTime = endDate
          }

          // 设置times字段用于兼容
          this.$set(this.dataForm, "times", [this.dataForm.startTime, this.dataForm.endTime])

          console.log('预填充时间信息:', {
            原始timeInfo: timeInfo,
            填充后的表单: {
              scheduleDate: this.dataForm.scheduleDate,
              startTimeOnly: this.dataForm.startTimeOnly,
              endTimeOnly: this.dataForm.endTimeOnly,
              duration: this.dataForm.duration,
              startTime: this.dataForm.startTime,
              endTime: this.dataForm.endTime
            }
          })

        } catch (error) {
          console.error('预填充时间信息失败:', error)
          this.$message.warning('时间信息预填充失败，请手动设置')
        }
      }
    }
  }
</script>

<style scoped>
.sync-hint {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 12px;
  color: #409eff;
  display: flex;
  align-items: center;
  gap: 6px;
}

.sync-hint i {
  font-size: 14px;
}

.reset-hint {
  margin-top: 8px;
  padding: 8px 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  font-size: 12px;
  color: #f56c6c;
  display: flex;
  align-items: center;
  gap: 6px;
}

.reset-hint i {
  font-size: 14px;
}
</style>
