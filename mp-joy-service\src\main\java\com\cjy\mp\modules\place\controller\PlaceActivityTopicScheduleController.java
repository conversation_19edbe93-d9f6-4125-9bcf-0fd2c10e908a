package com.cjy.mp.modules.place.controller;

import java.io.IOException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.cjy.mp.modules.activity.entity.ActivityEntity;
import com.cjy.mp.modules.activity.entity.ActivityGuestEntity;
import com.cjy.mp.modules.activity.service.ActivityGuestService;
import com.cjy.mp.modules.activity.service.ActivityService;
import com.cjy.mp.modules.place.dto.ScheduleDto;
import com.cjy.mp.modules.place.dto.ScheduleErrDto;
import com.cjy.mp.modules.place.dto.ScheduleExcelDto;
import com.cjy.mp.modules.place.dto.ScheduleExcelErrDto;
import com.cjy.mp.modules.place.entity.*;
import com.cjy.mp.modules.place.export.PlaceActivityTopicScheduleExportService;
import com.cjy.mp.modules.place.service.*;
import com.cjy.mp.modules.activity.importExcel.UserDataListener;

import io.swagger.annotations.ApiOperation;


import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.cjy.mp.common.easyexcel.EasyExcelListener;
import com.cjy.mp.common.easyexcel.EasyExcelUtils;
import com.cjy.mp.common.easyexcel.ExcelCheckErrDto;
import com.cjy.mp.common.utils.PageUtils;
import com.cjy.mp.common.utils.R;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-09 10:49:40
 */
@RestController
@RequestMapping("place/placeactivitytopicschedule")
public class PlaceActivityTopicScheduleController {
    @Autowired
    private PlaceActivityTopicScheduleService placeActivityTopicScheduleService;
    @Autowired
    private PlaceActivityTopicService placeActivityTopicService;
    @Autowired
    private PlaceActivityTopicScheduleGuestService placeActivityTopicScheduleGuestService;
    @Autowired
    private PlaceActivityService placeActivityService;
    @Autowired
    private PlaceActivityTopicScheduleSpeakerService placeActivityTopicScheduleSpeakerService;
    @Autowired
    private PlaceActivityTopicScheduleDiscussService placeActivityTopicScheduleDiscussService;
    @Autowired
    private ActivityGuestService activityGuestService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private ScheduleImportService scheduleImportService;
    @Autowired
    private ScheduleExcelImportService scheduleExcelImportService;
    @Autowired
    private PlaceActivityTopicScheduleExportService scheduleExportService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("place:placeactivitytopicschedule:list")
    public R list(@RequestParam Map<String, Object> params) {
        PageUtils page = placeActivityTopicScheduleService.queryPage(params);

        return R.ok().put("page", page);
    }

    /**
     * 按主题分组的日程列表 - 用于管理端重构后的页面
     * 返回数据结构：主题包裹日程的层级结构，支持分页和筛选
     */
    @RequestMapping("/listByTopic")
    @RequiresPermissions("place:placeactivitytopicschedule:list")
    public R listByTopic(@RequestParam Map<String, Object> params){
        try {
            PageUtils page = placeActivityTopicScheduleService.queryPageByTopic(params);
            return R.ok().put("page", page);
        } catch (Exception e) {
            return R.error("获取主题分组日程列表失败：" + e.getMessage());
        }
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("place:placeactivitytopicschedule:info")
    public R info(@PathVariable("id") Long id) {
        PlaceActivityTopicScheduleEntity placeActivityTopicSchedule = placeActivityTopicScheduleService.getById(id);
        List<PlaceActivityTopicScheduleGuestEntity> placeActivityTopicScheduleGuestEntities = placeActivityTopicScheduleGuestService
                .selectByPlaceActivityTopicScheduleId(id);
        List<Long> scheduleGuestIds = placeActivityTopicScheduleGuestEntities.stream()
                .map(PlaceActivityTopicScheduleGuestEntity::getActivityGuestId).collect(Collectors.toList());
        List<PlaceActivityTopicScheduleSpeakerEntity> placeActivityTopicScheduleSpeakerEntities = placeActivityTopicScheduleSpeakerService
                .selectByPlaceActivityTopicScheduleId(id);
        List<Long> scheduleSpeakerIds = placeActivityTopicScheduleSpeakerEntities.stream()
                .map(PlaceActivityTopicScheduleSpeakerEntity::getActivityGuestId).collect(Collectors.toList());
        List<PlaceActivityTopicScheduleDiscussEntity> placeActivityTopicScheduleDiscussEntities = placeActivityTopicScheduleDiscussService
                .selectByPlaceActivityTopicScheduleId(id);
        List<Long> scheduleDiscussIds = placeActivityTopicScheduleDiscussEntities.stream()
                .map(PlaceActivityTopicScheduleDiscussEntity::getActivityGuestId).collect(Collectors.toList());
        placeActivityTopicSchedule.setScheduleGuestIds(scheduleGuestIds);
        placeActivityTopicSchedule.setScheduleSpeakerIds(scheduleSpeakerIds);
        placeActivityTopicSchedule.setScheduleDiscussIds(scheduleDiscussIds);
        return R.ok().put("placeActivityTopicSchedule", placeActivityTopicSchedule);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("place:placeactivitytopicschedule:save")
    public R save(@RequestBody PlaceActivityTopicScheduleEntity placeActivityTopicSchedule) {
        PlaceActivityTopicEntity placeActivityTopicEntity = placeActivityTopicService
                .getById(placeActivityTopicSchedule.getPlaceActivityTopicId());
        if (null == placeActivityTopicEntity) {
            return R.error("主题不存在");
        }
        placeActivityTopicSchedule.setPlaceId(placeActivityTopicEntity.getPlaceId());

        // 处理日程插入和时间调整（如果指定了插入位置）
        if (placeActivityTopicSchedule.getInsertPosition() != null) {
            R insertResult = placeActivityTopicScheduleService.saveWithInsertPosition(placeActivityTopicSchedule);
            if ((int) insertResult.get("code") == 500) {
                return insertResult;
            }
        } else {
            // 普通保存逻辑
            // 校验日程
            // R result = placeActivityTopicScheduleService.checkSchedulesTime(placeActivityTopicSchedule);
            // if ((int) result.get("code") == 500) {
            //     return result;
            // }
            placeActivityTopicScheduleService.save(placeActivityTopicSchedule);
        }

        ActivityEntity activityEntity = activityService.getById(placeActivityTopicSchedule.getActivityId());
        if (placeActivityTopicSchedule.getScheduleGuestIds().size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService
                    .findByIds(placeActivityTopicSchedule.getScheduleGuestIds()).stream()
                    .collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(), (e, v) -> e));

            List<PlaceActivityTopicScheduleGuestEntity> placeActivityTopicGuestEntities = placeActivityTopicSchedule
                    .getScheduleGuestIds().stream().map(e -> {
                        PlaceActivityTopicScheduleGuestEntity placeActivityTopicGuest = new PlaceActivityTopicScheduleGuestEntity();
                        placeActivityTopicGuest.setActivityId(placeActivityTopicSchedule.getActivityId());
                        placeActivityTopicGuest.setActivityGuestId(e);
                        placeActivityTopicGuest.setServiceFee(activityEntity.getScheduleGuest());
                        placeActivityTopicGuest.setPlaceActivityTopicScheduleId(placeActivityTopicSchedule.getId());
                        placeActivityTopicGuest
                                .setPlaceActivityTopicId(placeActivityTopicSchedule.getPlaceActivityTopicId());
                        ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                        placeActivityTopicGuest.setName(activityGuestEntity.getName());
                        placeActivityTopicGuest.setMobile(activityGuestEntity.getMobile());
                        placeActivityTopicGuest.setDuties(activityGuestEntity.getDuties());
                        placeActivityTopicGuest.setUnit(activityGuestEntity.getUnit());
                        return placeActivityTopicGuest;
                    }).collect(Collectors.toList());
            placeActivityTopicScheduleGuestService.saveBatch(placeActivityTopicGuestEntities);
        }

        if (placeActivityTopicSchedule.getScheduleSpeakerIds().size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService
                    .findByIds(placeActivityTopicSchedule.getScheduleSpeakerIds()).stream()
                    .collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(), (e, v) -> e));

            List<PlaceActivityTopicScheduleSpeakerEntity> placeActivityTopicSpeakerEntities = placeActivityTopicSchedule
                    .getScheduleSpeakerIds().stream().map(e -> {
                        PlaceActivityTopicScheduleSpeakerEntity placeActivityTopicSpeaker = new PlaceActivityTopicScheduleSpeakerEntity();
                        placeActivityTopicSpeaker.setActivityId(placeActivityTopicSchedule.getActivityId());
                        placeActivityTopicSpeaker.setActivityGuestId(e);
                        placeActivityTopicSpeaker.setServiceFee(activityEntity.getScheduleSpeaker());
                        placeActivityTopicSpeaker.setPlaceActivityTopicScheduleId(placeActivityTopicSchedule.getId());
                        placeActivityTopicSpeaker
                                .setPlaceActivityTopicId(placeActivityTopicSchedule.getPlaceActivityTopicId());
                        ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                        placeActivityTopicSpeaker.setName(activityGuestEntity.getName());
                        placeActivityTopicSpeaker.setMobile(activityGuestEntity.getMobile());
                        placeActivityTopicSpeaker.setDuties(activityGuestEntity.getDuties());
                        placeActivityTopicSpeaker.setUnit(activityGuestEntity.getUnit());
                        return placeActivityTopicSpeaker;
                    }).collect(Collectors.toList());
            placeActivityTopicScheduleSpeakerService.saveBatch(placeActivityTopicSpeakerEntities);
        }

        if (placeActivityTopicSchedule.getScheduleDiscussIds().size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService
                    .findByIds(placeActivityTopicSchedule.getScheduleDiscussIds()).stream()
                    .collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(), (e, v) -> e));

            List<PlaceActivityTopicScheduleDiscussEntity> placeActivityTopicScheduleDiscussEntities = placeActivityTopicSchedule
                    .getScheduleDiscussIds().stream().map(e -> {
                        PlaceActivityTopicScheduleDiscussEntity placeActivityTopicScheduleDiscuss = new PlaceActivityTopicScheduleDiscussEntity();
                        placeActivityTopicScheduleDiscuss.setActivityId(placeActivityTopicSchedule.getActivityId());
                        placeActivityTopicScheduleDiscuss.setActivityGuestId(e);
                        placeActivityTopicScheduleDiscuss.setServiceFee(activityEntity.getScheduleDiscuss());
                        placeActivityTopicScheduleDiscuss
                                .setPlaceActivityTopicScheduleId(placeActivityTopicSchedule.getId());
                        placeActivityTopicScheduleDiscuss
                                .setPlaceActivityTopicId(placeActivityTopicSchedule.getPlaceActivityTopicId());
                        ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                        placeActivityTopicScheduleDiscuss.setName(activityGuestEntity.getName());
                        placeActivityTopicScheduleDiscuss.setMobile(activityGuestEntity.getMobile());
                        placeActivityTopicScheduleDiscuss.setDuties(activityGuestEntity.getDuties());
                        placeActivityTopicScheduleDiscuss.setUnit(activityGuestEntity.getUnit());
                        return placeActivityTopicScheduleDiscuss;
                    }).collect(Collectors.toList());
            placeActivityTopicScheduleDiscussService.saveBatch(placeActivityTopicScheduleDiscussEntities);
        }
        // 合并用户ID
        List<Long> activityGuestIds = Stream.of(placeActivityTopicSchedule.getScheduleGuestIds(),
                placeActivityTopicSchedule.getScheduleSpeakerIds(), placeActivityTopicSchedule.getScheduleDiscussIds())
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        activityGuestService.updateServiceFeeIds(activityGuestIds);
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("place:placeactivitytopicschedule:update")
    public R update(@RequestBody PlaceActivityTopicScheduleEntity placeActivityTopicSchedule) {
        PlaceActivityTopicEntity placeActivityTopicEntity = placeActivityTopicService
                .getById(placeActivityTopicSchedule.getPlaceActivityTopicId());
        if (null == placeActivityTopicEntity) {
            return R.error("主题不存在");
        }
        placeActivityTopicSchedule.setPlaceId(placeActivityTopicEntity.getPlaceId());

        // 从前端获取是否重置确认状态的标志
        Boolean resetConfirmStatus = placeActivityTopicSchedule.getResetConfirmStatus();
        boolean needResetConfirmStatus = resetConfirmStatus != null ? resetConfirmStatus : false;

        // 校验日程
        // R result = placeActivityTopicScheduleService.checkSchedulesTime(placeActivityTopicSchedule);
        // if ((int) result.get("code") == 500) {
        //     return result;
        // }

        // 处理时间变化的同步调整（根据用户选择）
        Boolean syncFollowingSchedules = placeActivityTopicSchedule.getSyncFollowingSchedules();
        if (Boolean.TRUE.equals(syncFollowingSchedules)) {
            R updateResult = placeActivityTopicScheduleService.updateWithTimeSync(placeActivityTopicSchedule);
            if ((int) updateResult.get("code") == 500) {
                return updateResult;
            }
        } else {
            // 普通更新，不同步后续日程
            placeActivityTopicScheduleService.updateById(placeActivityTopicSchedule);
        }
        ActivityEntity activityEntity = activityService.getById(placeActivityTopicSchedule.getActivityId());
        // 讲者
        List<PlaceActivityTopicScheduleGuestEntity> placeActivityTopicGuestEntities = placeActivityTopicScheduleGuestService
                .selectByPlaceActivityTopicScheduleId(placeActivityTopicSchedule.getId());
        List<Long> exitTopicGuestIds = placeActivityTopicGuestEntities.stream()
                .map(PlaceActivityTopicScheduleGuestEntity::getActivityGuestId).collect(Collectors.toList());
        List<Long> exitTopicScheduleGuestIds = placeActivityTopicGuestEntities.stream()
                .map(PlaceActivityTopicScheduleGuestEntity::getId).collect(Collectors.toList());
        List<Long> schedulesGuestIds = placeActivityTopicSchedule.getScheduleGuestIds();
        if (schedulesGuestIds.size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService.findByIds(schedulesGuestIds)
                    .stream().collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(), (e, v) -> e));
            // 找到没动的数据，重新设置topicId
            List<PlaceActivityTopicScheduleGuestEntity> updateGuest = placeActivityTopicGuestEntities.stream()
                    .filter(e -> schedulesGuestIds.contains(e.getActivityGuestId()))
                    .peek(e -> e.setPlaceActivityTopicId(placeActivityTopicSchedule.getPlaceActivityTopicId()))
                    .collect(Collectors.toList());
            // 找到删除
            List<Long> deleteGuest = placeActivityTopicGuestEntities.stream()
                    .filter(e -> !schedulesGuestIds.contains(e.getActivityGuestId()))
                    .map(PlaceActivityTopicScheduleGuestEntity::getId).collect(Collectors.toList());
            List<PlaceActivityTopicScheduleGuestEntity> saveGuest = schedulesGuestIds.stream()
                    .filter(f -> !exitTopicGuestIds.contains(f)).map(e -> {
                        PlaceActivityTopicScheduleGuestEntity placeActivityTopicGuest = new PlaceActivityTopicScheduleGuestEntity();
                        placeActivityTopicGuest.setActivityId(placeActivityTopicSchedule.getActivityId());
                        placeActivityTopicGuest.setActivityGuestId(e);
                        placeActivityTopicGuest.setServiceFee(activityEntity.getScheduleGuest());
                        placeActivityTopicGuest.setPlaceActivityTopicScheduleId(placeActivityTopicSchedule.getId());
                        placeActivityTopicGuest
                                .setPlaceActivityTopicId(placeActivityTopicSchedule.getPlaceActivityTopicId());
                        ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                        placeActivityTopicGuest.setName(activityGuestEntity.getName());
                        placeActivityTopicGuest.setMobile(activityGuestEntity.getMobile());
                        placeActivityTopicGuest.setDuties(activityGuestEntity.getDuties());
                        placeActivityTopicGuest.setUnit(activityGuestEntity.getUnit());
                        return placeActivityTopicGuest;
                    }).collect(Collectors.toList());
            if (updateGuest.size() > 0) {
                placeActivityTopicScheduleGuestService.updateBatchById(updateGuest);
            }
            if (deleteGuest.size() > 0) {
                placeActivityTopicScheduleGuestService.removeByIds(deleteGuest);
            }
            if (saveGuest.size() > 0) {
                placeActivityTopicScheduleGuestService.saveBatch(saveGuest);
            }
        } else {
            if (placeActivityTopicGuestEntities.size() > 0) {
                placeActivityTopicScheduleGuestService.removeByIds(exitTopicScheduleGuestIds);
            }
        }
        // 主持
        List<PlaceActivityTopicScheduleSpeakerEntity> placeActivityTopicSpeakerEntities = placeActivityTopicScheduleSpeakerService
                .selectByPlaceActivityTopicScheduleId(placeActivityTopicSchedule.getId());
        List<Long> exitTopicSpeakerIds = placeActivityTopicSpeakerEntities.stream()
                .map(PlaceActivityTopicScheduleSpeakerEntity::getActivityGuestId).collect(Collectors.toList());
        List<Long> exitTopicScheduleSpeakerIds = placeActivityTopicSpeakerEntities.stream()
                .map(PlaceActivityTopicScheduleSpeakerEntity::getId).collect(Collectors.toList());
        List<Long> schedulesSpeakerIds = placeActivityTopicSchedule.getScheduleSpeakerIds();
        if (schedulesSpeakerIds.size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService.findByIds(schedulesSpeakerIds)
                    .stream().collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(), (e, v) -> e));
            // 找到没动的数据，重新设置topicId
            List<PlaceActivityTopicScheduleSpeakerEntity> updateGuest = placeActivityTopicSpeakerEntities.stream()
                    .filter(e -> schedulesGuestIds.contains(e.getActivityGuestId()))
                    .peek(e -> e.setPlaceActivityTopicId(placeActivityTopicSchedule.getPlaceActivityTopicId()))
                    .collect(Collectors.toList());
            // 找到删除
            List<Long> deleteSpeaker = placeActivityTopicSpeakerEntities.stream()
                    .filter(e -> !schedulesSpeakerIds.contains(e.getActivityGuestId()))
                    .map(PlaceActivityTopicScheduleSpeakerEntity::getId).collect(Collectors.toList());
            List<PlaceActivityTopicScheduleSpeakerEntity> saveSpeaker = schedulesSpeakerIds.stream()
                    .filter(f -> !exitTopicSpeakerIds.contains(f)).map(e -> {
                        PlaceActivityTopicScheduleSpeakerEntity placeActivityTopicSpeaker = new PlaceActivityTopicScheduleSpeakerEntity();
                        placeActivityTopicSpeaker.setActivityId(placeActivityTopicSchedule.getActivityId());
                        placeActivityTopicSpeaker.setActivityGuestId(e);
                        placeActivityTopicSpeaker.setServiceFee(activityEntity.getScheduleSpeaker());
                        placeActivityTopicSpeaker.setPlaceActivityTopicScheduleId(placeActivityTopicSchedule.getId());
                        placeActivityTopicSpeaker
                                .setPlaceActivityTopicId(placeActivityTopicSchedule.getPlaceActivityTopicId());
                        ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                        placeActivityTopicSpeaker.setName(activityGuestEntity.getName());
                        placeActivityTopicSpeaker.setMobile(activityGuestEntity.getMobile());
                        placeActivityTopicSpeaker.setDuties(activityGuestEntity.getDuties());
                        placeActivityTopicSpeaker.setUnit(activityGuestEntity.getUnit());
                        return placeActivityTopicSpeaker;
                    }).collect(Collectors.toList());
            if (updateGuest.size() > 0) {
                placeActivityTopicScheduleSpeakerService.updateBatchById(updateGuest);
            }
            if (deleteSpeaker.size() > 0) {
                placeActivityTopicScheduleSpeakerService.removeByIds(deleteSpeaker);
            }
            if (saveSpeaker.size() > 0) {
                placeActivityTopicScheduleSpeakerService.saveBatch(saveSpeaker);
            }
        } else {
            if (placeActivityTopicSpeakerEntities.size() > 0) {
                placeActivityTopicScheduleSpeakerService.removeByIds(exitTopicScheduleSpeakerIds);
            }
        }
        // 讨论
        List<PlaceActivityTopicScheduleDiscussEntity> placeActivityTopicDiscussEntities = placeActivityTopicScheduleDiscussService
                .selectByPlaceActivityTopicScheduleId(placeActivityTopicSchedule.getId());
        List<Long> exitTopicDiscussIds = placeActivityTopicDiscussEntities.stream()
                .map(PlaceActivityTopicScheduleDiscussEntity::getActivityGuestId).collect(Collectors.toList());
        List<Long> exitTopicScheduleDiscussIds = placeActivityTopicDiscussEntities.stream()
                .map(PlaceActivityTopicScheduleDiscussEntity::getId).collect(Collectors.toList());
        List<Long> schedulesDiscussIds = placeActivityTopicSchedule.getScheduleDiscussIds();
        if (schedulesDiscussIds.size() > 0) {
            Map<Long, ActivityGuestEntity> activityGuestEntities = activityGuestService.findByIds(schedulesDiscussIds)
                    .stream().collect(Collectors.toMap(ActivityGuestEntity::getId, Function.identity(), (e, v) -> e));
            // 找到没动的数据，重新设置topicId
            List<PlaceActivityTopicScheduleDiscussEntity> updateGuest = placeActivityTopicDiscussEntities.stream()
                    .filter(e -> schedulesGuestIds.contains(e.getActivityGuestId()))
                    .peek(e -> e.setPlaceActivityTopicId(placeActivityTopicSchedule.getPlaceActivityTopicId()))
                    .collect(Collectors.toList());
            // 找到删除
            List<Long> deleteDiscuss = placeActivityTopicDiscussEntities.stream()
                    .filter(e -> !schedulesDiscussIds.contains(e.getActivityGuestId()))
                    .map(PlaceActivityTopicScheduleDiscussEntity::getId).collect(Collectors.toList());
            List<PlaceActivityTopicScheduleDiscussEntity> saveDiscuss = schedulesDiscussIds.stream()
                    .filter(f -> !exitTopicDiscussIds.contains(f)).map(e -> {
                        PlaceActivityTopicScheduleDiscussEntity placeActivityTopicDiscuss = new PlaceActivityTopicScheduleDiscussEntity();
                        placeActivityTopicDiscuss.setActivityId(placeActivityTopicSchedule.getActivityId());
                        placeActivityTopicDiscuss.setActivityGuestId(e);
                        placeActivityTopicDiscuss.setServiceFee(activityEntity.getScheduleDiscuss());
                        placeActivityTopicDiscuss.setPlaceActivityTopicScheduleId(placeActivityTopicSchedule.getId());
                        placeActivityTopicDiscuss
                                .setPlaceActivityTopicId(placeActivityTopicSchedule.getPlaceActivityTopicId());
                        ActivityGuestEntity activityGuestEntity = activityGuestEntities.get(e);
                        placeActivityTopicDiscuss.setName(activityGuestEntity.getName());
                        placeActivityTopicDiscuss.setMobile(activityGuestEntity.getMobile());
                        placeActivityTopicDiscuss.setDuties(activityGuestEntity.getDuties());
                        placeActivityTopicDiscuss.setUnit(activityGuestEntity.getUnit());
                        return placeActivityTopicDiscuss;
                    }).collect(Collectors.toList());
            if (updateGuest.size() > 0) {
                placeActivityTopicScheduleDiscussService.updateBatchById(updateGuest);
            }
            if (deleteDiscuss.size() > 0) {
                placeActivityTopicScheduleDiscussService.removeByIds(deleteDiscuss);
            }
            if (saveDiscuss.size() > 0) {
                placeActivityTopicScheduleDiscussService.saveBatch(saveDiscuss);
            }
        } else {
            if (placeActivityTopicDiscussEntities.size() > 0) {
                placeActivityTopicScheduleDiscussService.removeByIds(exitTopicScheduleDiscussIds);
            }
        }
        // 合并用户ID
        List<Long> activityGuestIds = Stream.of(schedulesGuestIds, schedulesSpeakerIds, schedulesDiscussIds)
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        activityGuestService.updateServiceFeeIds(activityGuestIds);

        // 如果需要重置确认状态，重置相关确认状态
        if (needResetConfirmStatus) {
            boolean hasConfirmedTasks = resetConfirmStatusForSchedule(placeActivityTopicSchedule.getId(), activityGuestIds);
            if (hasConfirmedTasks) {
                return R.ok().put("hasResetConfirmedTasks", true).put("msg", "日程已更新，已重置专家确认状态，专家将收到重新确认提示");
            }
        }

        return R.ok();
    }

    /**
     * 重置日程相关的确认状态
     * @param scheduleId 日程ID
     * @param activityGuestIds 相关的嘉宾ID列表
     * @return 是否有已确认的任务被重置
     */
    private boolean resetConfirmStatusForSchedule(Long scheduleId, List<Long> activityGuestIds) {
        boolean hasConfirmedTasks = false;

        // 重置place_activity_topic_schedule_guest的confirm_status为0（未确认）
        List<PlaceActivityTopicScheduleGuestEntity> scheduleGuests = placeActivityTopicScheduleGuestService.selectByPlaceActivityTopicScheduleId(scheduleId);
        if (!scheduleGuests.isEmpty()) {
            // 检查是否有已确认的任务
            hasConfirmedTasks = hasConfirmedTasks || scheduleGuests.stream().anyMatch(guest -> guest.getConfirmStatus() != null && guest.getConfirmStatus() == 1);
            scheduleGuests.forEach(guest -> guest.setConfirmStatus(0));
            placeActivityTopicScheduleGuestService.updateBatchById(scheduleGuests);
        }

        // 重置place_activity_topic_schedule_speaker的confirm_status为0（未确认）
        List<PlaceActivityTopicScheduleSpeakerEntity> scheduleSpeakers = placeActivityTopicScheduleSpeakerService.selectByPlaceActivityTopicScheduleId(scheduleId);
        if (!scheduleSpeakers.isEmpty()) {
            // 检查是否有已确认的任务
            hasConfirmedTasks = hasConfirmedTasks || scheduleSpeakers.stream().anyMatch(speaker -> speaker.getConfirmStatus() != null && speaker.getConfirmStatus() == 1);
            scheduleSpeakers.forEach(speaker -> speaker.setConfirmStatus(0));
            placeActivityTopicScheduleSpeakerService.updateBatchById(scheduleSpeakers);
        }

        // 重置place_activity_topic_schedule_discuss的confirm_status为0（未确认）
        List<PlaceActivityTopicScheduleDiscussEntity> scheduleDiscusses = placeActivityTopicScheduleDiscussService.selectByPlaceActivityTopicScheduleId(scheduleId);
        if (!scheduleDiscusses.isEmpty()) {
            // 检查是否有已确认的任务
            hasConfirmedTasks = hasConfirmedTasks || scheduleDiscusses.stream().anyMatch(discuss -> discuss.getConfirmStatus() != null && discuss.getConfirmStatus() == 1);
            scheduleDiscusses.forEach(discuss -> discuss.setConfirmStatus(0));
            placeActivityTopicScheduleDiscussService.updateBatchById(scheduleDiscusses);
        }

        // 重置相关activity_guest的is_service为0
        if (activityGuestIds != null && !activityGuestIds.isEmpty()) {
            List<ActivityGuestEntity> activityGuests = activityGuestService.findByIds(activityGuestIds);
            if (!activityGuests.isEmpty()) {
                activityGuests.forEach(guest -> guest.setIsSchedule(0));
                activityGuestService.updateBatchById(activityGuests);
            }
        }

        return hasConfirmedTasks;
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("place:placeactivitytopicschedule:delete")
    @Transactional(rollbackFor = Exception.class)
    public R delete(@RequestBody Map<String, Object> params) {
            Long[] ids = null;
            Boolean autoAdjustTime = false;

            // 兼容原有的数组格式和新的对象格式
            if (params.containsKey("ids")) {
                Object idsObj = params.get("ids");
                if (idsObj instanceof List) {
                    List<String> idList = (List<String>) idsObj;
                    ids = idList.stream().map(Long::valueOf).toArray(Long[]::new);
                } else if (idsObj instanceof Long[]) {
                    ids = (Long[]) idsObj;
                }
                autoAdjustTime = Boolean.TRUE.equals(params.get("autoAdjustTime"));
            } else {
                // 兼容原有格式，尝试直接解析为数组
                Object[] objArray = params.values().toArray();
                if (objArray.length > 0 && objArray[0] instanceof Number) {
                    ids = Arrays.stream(objArray)
                            .map(obj -> Long.valueOf(obj.toString()))
                            .toArray(Long[]::new);
                }
            }

            if (ids == null || ids.length == 0) {
                return R.error("删除ID不能为空");
            }

            // 如果需要自动调整时间且只删除一个日程
            if (autoAdjustTime && ids.length == 1) {
                R result = placeActivityTopicScheduleService.deleteWithTimeAdjustment(ids[0]);
                if ((int) result.get("code") == 500) {
                    return result;
                }
            } else {
                // 普通删除
                placeActivityTopicScheduleService.removeByIds(Arrays.asList(ids));
                // 删除讲者
                List<PlaceActivityTopicScheduleGuestEntity> placeActivityTopicScheduleGuestEntities = placeActivityTopicScheduleGuestService
                        .selectByPlaceActivityTopicScheduleIds(Arrays.asList(ids));
                placeActivityTopicScheduleGuestService.removeByIds(placeActivityTopicScheduleGuestEntities.stream()
                        .map(PlaceActivityTopicScheduleGuestEntity::getId).collect(Collectors.toList()));
                // 删除主持
                List<PlaceActivityTopicScheduleSpeakerEntity> placeActivityTopicScheduleSpeakerEntities = placeActivityTopicScheduleSpeakerService
                        .selectByPlaceActivityTopicScheduleIds(Arrays.asList(ids));
                placeActivityTopicScheduleSpeakerService.removeByIds(placeActivityTopicScheduleSpeakerEntities.stream()
                        .map(PlaceActivityTopicScheduleSpeakerEntity::getId).collect(Collectors.toList()));
                // 删除讨论
                List<PlaceActivityTopicScheduleDiscussEntity> placeActivityTopicScheduleDiscussEntities = placeActivityTopicScheduleDiscussService
                        .selectByPlaceActivityTopicScheduleIds(Arrays.asList(ids));
                placeActivityTopicScheduleDiscussService.removeByIds(placeActivityTopicScheduleDiscussEntities.stream()
                        .map(PlaceActivityTopicScheduleDiscussEntity::getId).collect(Collectors.toList()));
            }

            return R.ok();
    }

    /**
     * 移动日程位置
     */
    @RequestMapping("/move")
    @RequiresPermissions("place:placeactivitytopicschedule:update")
    public R moveSchedule(@RequestBody Map<String, Object> params) {
        try {
            Long scheduleId = Long.valueOf(params.get("scheduleId").toString());
            Long topicId = Long.valueOf(params.get("topicId").toString());
            String direction = params.get("direction").toString();

            R result = placeActivityTopicScheduleService.moveSchedule(scheduleId, topicId, direction);
            return result;
        } catch (Exception e) {
            return R.error("移动失败：" + e.getMessage());
        }
    }

    /**
     * 同步主题下所有日程的时间，使其连贯
     */
    @RequestMapping("/syncTimes")
    @RequiresPermissions("place:placeactivitytopicschedule:update")
    public R syncScheduleTimes(@RequestBody Map<String, Object> params) {
        try {
            Long topicId = Long.valueOf(params.get("topicId").toString());
            String startTimeStr = params.get("startTime").toString();

            R result = placeActivityTopicScheduleService.syncScheduleTimes(topicId, startTimeStr);
            return result;
        } catch (Exception e) {
            return R.error("时间同步失败：" + e.getMessage());
        }
    }

    @RequestMapping("/downloadDemo")
    @ApiOperation(value = "日程导入模板")
    public void downloadDemo(HttpServletResponse response) throws IOException {
        EasyExcelUtils.webWriteExcel(response, null, ScheduleDto.class, "日程导入模板");
    }

    @RequestMapping("/downloadExcelFormatDemo")
    @ApiOperation(value = "Excel格式日程导入模板")
    public void downloadExcelFormatDemo(HttpServletResponse response) throws IOException {
        EasyExcelUtils.webWriteExcel(response, null, ScheduleExcelDto.class, "Excel格式日程导入模板");
    }

    @PostMapping("/importExcel")
    public R importExcel(HttpServletResponse response, @RequestParam MultipartFile file,
            @RequestParam("activityId") Long activityId, @RequestParam("appid") String appid) throws IOException {
        EasyExcelListener easyExcelListener = new EasyExcelListener(scheduleImportService, ScheduleDto.class,
                activityId, appid);
        EasyExcelFactory.read(file.getInputStream(), ScheduleDto.class, easyExcelListener).sheet().doRead();
        List<ExcelCheckErrDto<ScheduleDto>> errList = easyExcelListener.getErrList();
        if (!errList.isEmpty()) {
            // 如果包含错误信息就导出错误信息
            List<ScheduleErrDto> excelErrDtos = errList.stream().map(excelCheckErrDto -> {
                ScheduleErrDto userExcelErrDto = JSON.parseObject(JSON.toJSONString(excelCheckErrDto.getT()),
                        ScheduleErrDto.class);
                userExcelErrDto.setErrMsg(excelCheckErrDto.getErrMsg());
                return userExcelErrDto;
            }).collect(Collectors.toList());
            EasyExcelUtils.webWriteExcel(response, excelErrDtos, ScheduleErrDto.class, "日程导入错误数据");
        }
        return R.ok();
    }

    /**
     * Excel格式日程导入
     * 
     * @param response   HTTP响应
     * @param file       上传的Excel文件
     * @param placeId    会场ID (由参数传递，不新增place实体)
     * @param activityId 活动ID
     * @param appid      应用ID
     * @return 导入结果
     * @throws IOException
     */
    @PostMapping("/importExcelFormat")
    @ApiOperation(value = "Excel格式日程导入")
    public R importExcelFormat(HttpServletResponse response,
            @RequestParam MultipartFile file,
            @RequestParam("placeId") Long placeId,
            @RequestParam("activityId") Long activityId,
            @RequestParam("appid") String appid) throws IOException {

        try {
            // 读取Excel文件的所有数据
            UserDataListener userDataListener = new UserDataListener();
            EasyExcel.read(file.getInputStream(), userDataListener).sheet().doRead();

            // 转换数据格式
            List<List<String>> excelData = new ArrayList<>();
            // 添加表头数据
            for (Map<Integer, String> headRow : userDataListener.getHeadList()) {
                List<String> rowData = new ArrayList<>();
                for (int i = 0; i < 10; i++) { // 假设最多10列
                    rowData.add(headRow.getOrDefault(i, ""));
                }
                excelData.add(rowData);
            }
            // 添加数据行
            for (Map<Integer, String> dataRow : userDataListener.getDataList()) {
                List<String> rowData = new ArrayList<>();
                for (int i = 0; i < 10; i++) { // 假设最多10列
                    rowData.add(dataRow.getOrDefault(i, ""));
                }
                excelData.add(rowData);
            }

            // 解析Excel数据
            List<ScheduleExcelDto> scheduleExcelDtos = scheduleExcelImportService.parseExcelData(excelData);

            if (scheduleExcelDtos.isEmpty()) {
                return R.error("未找到有效的数据行");
            }

            // 执行导入
            com.cjy.mp.common.easyexcel.ExcelCheckResult<ScheduleExcelDto> result = scheduleExcelImportService
                    .checkImportExcel(
                            scheduleExcelDtos, placeId, activityId, appid);

            List<com.cjy.mp.common.easyexcel.ExcelCheckErrDto<ScheduleExcelDto>> errList = result.getErrDtos();

            if (!errList.isEmpty()) {
                // 如果包含错误信息就导出错误信息
                List<ScheduleExcelErrDto> excelErrDtos = errList.stream().map(excelCheckErrDto -> {
                    ScheduleExcelErrDto userExcelErrDto = JSON.parseObject(JSON.toJSONString(excelCheckErrDto.getT()),
                            ScheduleExcelErrDto.class);
                    userExcelErrDto.setErrMsg(excelCheckErrDto.getErrMsg());
                    return userExcelErrDto;
                }).collect(Collectors.toList());
                EasyExcelUtils.webWriteExcel(response, excelErrDtos, ScheduleExcelErrDto.class, "Excel格式日程导入错误数据");
            }

            return R.ok().put("successCount", result.getSuccessDtos().size())
                    .put("errorCount", errList.size());

        } catch (Exception e) {
            return R.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出日程Word文档
     */
    @RequestMapping("/exportScheduleWord")
    public void exportScheduleWord(@RequestParam Map<String, Object> params,
            HttpServletRequest request, HttpServletResponse response) {
        // 获取参数
        String activityIdStr = (String) params.get("activityId");
        String date = (String) params.get("date");

        if (activityIdStr == null) {
            throw new RuntimeException("活动ID不能为空");
        }

        Long activityId = Long.parseLong(activityIdStr);

        // 获取活动信息
        ActivityEntity activity = activityService.getById(activityId);
        String activityName = activity != null ? activity.getName() : "会议";

        // 获取日程数据 - 使用与列表相同的查询逻辑
        PageUtils pageUtils = placeActivityTopicService.queryWxPage(params);
        List<PlaceActivityTopicEntity> topics = (List<PlaceActivityTopicEntity>) pageUtils.getList();

        // 导出Word
        scheduleExportService.exportScheduleWord(activityName, date, topics, request, response);

    }


    /**
     * 导出日程Word文档（分页版本 - 每个主题一页）
     */
    @RequestMapping("/exportScheduleWordWithPageBreak")
    public void exportScheduleWordWithPageBreak(@RequestParam Map<String, Object> params,
            HttpServletRequest request, HttpServletResponse response) {

        String activityIdStr = (String) params.get("activityId");
        String date = (String) params.get("date");

        if (activityIdStr == null) {
            throw new RuntimeException("活动ID不能为空");
        }

        Long activityId = Long.parseLong(activityIdStr);

        // 获取活动信息
        ActivityEntity activity = activityService.getById(activityId);
        String activityName = activity != null ? activity.getName() : "会议";

        // 获取日程数据 - 使用与列表相同的查询逻辑
        PageUtils pageUtils = placeActivityTopicService.queryWxPage(params);
        List<PlaceActivityTopicEntity> topics = (List<PlaceActivityTopicEntity>) pageUtils.getList();

        // 导出分页版本
        scheduleExportService.exportScheduleWordWithPageBreak(activityName, date, topics, request, response);
    }

    /**
     * 导出日程Excel文档
     */
    @RequestMapping("/exportScheduleExcel")
    @ApiOperation(value = "导出日程Excel文档")
    public void exportScheduleExcel(@RequestParam Map<String, Object> params,
            HttpServletRequest request, HttpServletResponse response) {

        String activityIdStr = (String) params.get("activityId");
        String date = (String) params.get("date");

        if (activityIdStr == null) {
            throw new RuntimeException("活动ID不能为空");
        }

        Long activityId = Long.parseLong(activityIdStr);

        // 获取活动信息
        ActivityEntity activity = activityService.getById(activityId);
        String activityName = activity != null ? activity.getName() : "会议";

        // 获取日程数据 - 使用与列表相同的查询逻辑
        PageUtils pageUtils = placeActivityTopicService.queryWxPage(params);
        List<PlaceActivityTopicEntity> topics = (List<PlaceActivityTopicEntity>) pageUtils.getList();

        // 导出Excel
        scheduleExportService.exportScheduleExcel(activityName, date, topics, request, response);
    }

    /**
     * 统计将要清理的孤立关联数据数量（日程相关）
     */
    @RequestMapping("/countOrphanedRelations")
    @RequiresPermissions("place:placeactivitytopicschedule:list")
    public R countOrphanedRelations(@RequestParam Map<String, Object> params){
        List<Map<String, Object>> result = placeActivityTopicService.countOrphanedRelations(params);
        // 只返回日程相关的数据
        List<Map<String, Object>> scheduleResult = result.stream()
                .filter(item -> {
                    String relationType = (String) item.get("relationType");
                    return relationType.startsWith("schedule_");
                })
                .collect(Collectors.toList());
        return R.ok().put("result", scheduleResult);
    }

    /**
     * 清理孤立的关联数据（日程相关）
     */
    @RequestMapping("/cleanOrphanedRelations")
    @RequiresPermissions("place:placeactivitytopicschedule:delete")
    public R cleanOrphanedRelations(@RequestParam Map<String, Object> params){
        // 先统计要清理的数据
        List<Map<String, Object>> countResult = placeActivityTopicService.countOrphanedRelations(params);
        List<Map<String, Object>> scheduleCountResult = countResult.stream()
                .filter(item -> {
                    String relationType = (String) item.get("relationType");
                    return relationType.startsWith("schedule_");
                })
                .collect(Collectors.toList());

        // 执行清理
        Map<String, Object> cleanResult = placeActivityTopicService.cleanOrphanedRelations(params);

        // 只返回日程相关的清理结果
        Map<String, Object> scheduleCleanResult = new HashMap<>();
        scheduleCleanResult.put("scheduleGuestCleaned", cleanResult.get("scheduleGuestCleaned"));
        scheduleCleanResult.put("scheduleSpeakerCleaned", cleanResult.get("scheduleSpeakerCleaned"));
        scheduleCleanResult.put("scheduleDiscussCleaned", cleanResult.get("scheduleDiscussCleaned"));

        int totalScheduleCleaned = (Integer) cleanResult.get("scheduleGuestCleaned") +
                                  (Integer) cleanResult.get("scheduleSpeakerCleaned") +
                                  (Integer) cleanResult.get("scheduleDiscussCleaned");
        scheduleCleanResult.put("totalScheduleCleaned", totalScheduleCleaned);

        // 返回清理前统计和清理结果
        Map<String, Object> result = new HashMap<>();
        result.put("beforeClean", scheduleCountResult);
        result.put("cleanResult", scheduleCleanResult);

        return R.ok().put("result", result);
    }

}
