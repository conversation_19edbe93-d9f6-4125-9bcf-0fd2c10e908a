<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.taskStatus" placeholder="任务状态" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="有任务" value="withTask"></el-option>
          <el-option label="无任务" value="withoutTask"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="searchList()">查询</el-button>
        <el-button v-if="isAuth('activity:activityguest:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activityguest:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button v-if="activityInfo.isFirstChar" type="danger" @click="updateIsFirstChar(0)">关闭嘉宾首字母排序</el-button>
        <el-button v-else type="success" @click="updateIsFirstChar(1)">开启嘉宾首字母排序</el-button>
        <el-button @click="exportHandle()" type="success">导出</el-button>

        <el-button @click="activityguestplane()" type="success">来程返程信息</el-button>
        <el-button @click="activityconfigupdateTurn()" type="primary">任务规则配置</el-button>
        <el-button @click="downloadDemo()" type="success">导入模板</el-button>
      </el-form-item>
      <el-form-item>
                <el-button @click="exportIndex()" type="success">导出首页通知短信</el-button>

        <!-- <el-dropdown @command="handleBatchCopyTemplateCommand" trigger="click" :disabled="dataListSelections.length <= 0">
          <el-button type="success">
            复制首页通知<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-tooltip placement="right" effect="light">
              <div slot="content" style="max-width: 400px; white-space: pre-wrap;">
                <strong>旧模板 (SMS_476755298)：</strong><br/>
                尊敬的[专家姓名]老师，欢迎参加[会议名称]，请您打开下方链接填写您的基本信息，感谢您对本次会议的支持，祝您生活愉快。填写链接： xxxxxxx
              </div>
              <el-dropdown-item command="template1">旧模板</el-dropdown-item>
            </el-tooltip>
            <el-tooltip placement="right" effect="light">
              <div slot="content" style="max-width: 400px; white-space: pre-wrap;">
                <strong>新模板 (SMS_489815559)：</strong><br/>
                尊敬的[专家姓名]教授： 您好！兹定于[会议时间]于[会议地点]召开[会议名称]。请您打开下方链接查阅您的相关任务邀请和基本信息填写，衷心感谢您对本次会议的大力支持。填写链接： xxxxxxx 会务组：[系统配置] 期待您的莅临和支持，盼复！ 大会组委会。
              </div>
              <el-dropdown-item command="template2">新模板</el-dropdown-item>
            </el-tooltip>
          </el-dropdown-menu>
        </el-dropdown> -->
        </el-form-item>
      <!-- <el-form-item>
        <el-dropdown @command="handleSmsTemplateCommand" trigger="click" :disabled="dataListSelections.length <= 0">
          <el-button type="primary">
            专家首页通知<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-tooltip placement="right" effect="light">
              <div slot="content" style="max-width: 400px; white-space: pre-wrap;">
                <strong>旧模板 (SMS_476755298)：</strong><br/>
                尊敬的${username}老师，欢迎参加${name}，请您打开下方链接填写您的基本信息，感谢您对本次会议的支持，祝您生活愉快。<br/>
                填写链接： xxxxxx
              </div>
              <el-dropdown-item command="template1">旧模板通知</el-dropdown-item>
            </el-tooltip>
            <el-tooltip placement="right" effect="light">
              <div slot="content" style="max-width: 400px; white-space: pre-wrap;">
                <strong>新模板 (SMS_489815559)：</strong><br/>
                尊敬的${username}教授： 您好！兹定于${time}于${place}召开${content}。<br/>
                请您打开下方链接查阅您的相关任务邀请和基本信息填写，衷心感谢您对本次会议的大力支持。<br/>
                填写链接： xxxxxxx<br/>
                会务组：${worker} 期待您的莅临和支持，盼复！ 大会组委会。<br/>
                <br/>
              </div>
              <el-dropdown-item command="template2">新模板通知</el-dropdown-item>
            </el-tooltip>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item> -->
      <el-form-item>
        <el-dropdown @command="handleImportCommand" trigger="click">
          <el-button type="primary">
            专家导入<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="mobile">按手机号匹配导入</el-dropdown-item>
            <el-dropdown-item command="name">按专家姓名匹配导入</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item>
        <el-dropdown @command="handleExportCommand" trigger="click">
          <el-button type="success">
            导出专家资料<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="all">导出全部资料</el-dropdown-item>
            <el-dropdown-item command="avatar">仅导出头像</el-dropdown-item>
            <el-dropdown-item command="introduction">仅导出介绍文件</el-dropdown-item>
            <el-dropdown-item command="idCard">仅导出身份证</el-dropdown-item>
            <el-dropdown-item command="custom">自定义选择</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="联系人姓名" width="120">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="联系人电话" width="130">
      </el-table-column>
      <el-table-column prop="unit" header-align="center" align="center" label="工作单位" width="150" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="duties" header-align="center" align="center" label="职称" width="100">
      </el-table-column>
      <el-table-column prop="englishName" header-align="center" align="center" label="英文名" width="120" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="expertType" header-align="center" align="center" label="专家类别" width="120">
        <div slot-scope="scope">
          <el-tag v-if="scope.row.expertType === '大陆专家'" type="primary">大陆专家</el-tag>
          <el-tag v-else-if="scope.row.expertType === '国外专家'" type="success">国外专家</el-tag>
          <el-tag v-else-if="scope.row.expertType === '港澳台专家'" type="warning">港澳台专家</el-tag>
          <span v-else>{{ scope.row.expertType }}</span>
        </div>
      </el-table-column>
      <el-table-column prop="assistantMobile" header-align="center" align="center" label="助手手机号" width="130">
      </el-table-column>
      <el-table-column prop="needHotel" header-align="center" align="center" label="是否需要酒店" width="120">
        <div slot-scope="scope">
          <el-tag :type="scope.row.needHotel == 1 ? 'success' : 'info'">{{ scope.row.needHotel == 1 ? '需要' : '不需要' }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="checkInDate" header-align="center" align="center" label="入住日期" width="100">
      </el-table-column>
      <el-table-column prop="checkOutDate" header-align="center" align="center" label="退房日期" width="100">
      </el-table-column>
      <el-table-column prop="idCard" header-align="center" align="center" label="身份证">
      </el-table-column>
      <el-table-column prop="avatar" header-align="center" align="center" label="头像">
        <div slot-scope="scope">
          <img class="image-sm" style="height: 80px" :src="scope.row.avatar" />
        </div>
      </el-table-column>
      <!-- <el-table-column prop="wxUserId" header-align="center" align="center" label="关联用户ID">
      </el-table-column> -->
      <el-table-column prop="idCardZheng" header-align="center" align="center" label="身份证正面">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.idCardZheng)" :src="scope.row.idCardZheng" />
          <a :href="scope.row.idCardZheng" target="_blank" v-else>{{ scope.row.idCardZheng }}</a>
        </div>
      </el-table-column>
      <el-table-column prop="idCardFan" header-align="center" align="center" label="身份证反面">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.idCardFan)" :src="scope.row.idCardFan" />
          <a :href="scope.row.idCardFan" target="_blank" v-else>{{ scope.row.idCardFan }}</a>
        </div>
      </el-table-column>
      <!-- <el-table-column prop="bank" header-align="center" align="center" label="银行卡号">
      </el-table-column>
      <el-table-column prop="kaihuhang" header-align="center" align="center" label="开户行">
      </el-table-column> -->
      <!-- <el-table-column prop="sendTask" header-align="center" align="center" label="学术短信发送">
        <div slot-scope="scope">
          <el-tag :type="scope.row.sendTask == 1 ? 'success' : 'danger'">{{ scope.row.sendTask == 1 ? '已发送' : '未发送'
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="sendTaskTime" header-align="center" align="center" label="发送时间">
      </el-table-column> -->
      <el-table-column prop="isAttend" header-align="center" align="center" label="是否参会">
        <div slot-scope="scope">
          <el-tag :type="scope.row.isAttend == 1 ? 'success' : 'danger'">{{ scope.row.isAttend == 1 ? '参会' : '不参会'
          }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="sendIndex" header-align="center" align="center" label="专家通知">
        <div slot-scope="scope">
          <el-tag :type="scope.row.sendIndex == 1 ? 'success' : 'danger'">{{ scope.row.sendIndex == 1 ? '已发送' : '未发送'
          }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="sendIndexTime" header-align="center" align="center" label="发送时间">
      </el-table-column>
      <el-table-column prop="sendIndexTime" header-align="center" align="center" label="专家简介">
        <div slot-scope="scope">
          <el-button @click="downloadFile(scope.row.contentFile)" size="mini"
            :type="!scope.row.contentFile ? 'danger' : 'success'">{{ !scope.row.contentFile ? '未上传' : '已上传(下载)'
            }}</el-button>
        </div>
      </el-table-column>
      <el-table-column prop="orderBy" header-align="center" align="center" label="排序">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <!-- <el-button type="text" size="small" @click="sendTaskHandle(scope.row.id)">专家议程确认通知</el-button> -->
          <!-- <el-dropdown @command="(command) => handleSingleSmsTemplateCommand(command, scope.row.id)" trigger="click">
            <el-button type="text" size="small">
              专家首页通知<i class="el-icon-arrow-down"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-tooltip placement="right" effect="light">
                <div slot="content" style="max-width: 400px; white-space: pre-wrap;">
                  <strong>旧模板 (SMS_476755298)：</strong><br/>
                  尊敬的${username}老师，欢迎参加${name}，请您打开下方链接填写您的基本信息，感谢您对本次会议的支持，祝您生活愉快。<br/>
                  填写链接： xxxxxx
                </div>
                <el-dropdown-item command="template1">旧模板通知</el-dropdown-item>
              </el-tooltip>
              <el-tooltip placement="right" effect="light">
                <div slot="content" style="max-width: 400px; white-space: pre-wrap;">
                  <strong>新模板 (SMS_489815559)：</strong><br/>
                  尊敬的${username}教授： 您好！兹定于${time}于${place}召开${content}。<br/>
                  请您打开下方链接查阅您的相关任务邀请和基本信息填写，衷心感谢您对本次会议的大力支持。<br/>
                  填写链接： xxxxxx<br/>
                  会务组：${worker} 期待您的莅临和支持，盼复！ 大会组委会。<br/>
                  <br/>
                </div>
                <el-dropdown-item command="template2">新模板通知</el-dropdown-item>
              </el-tooltip>
            </el-dropdown-menu>
          </el-dropdown> -->
          <el-button type="text" size="small" @click="showTaskHandle(scope.row.id)">学术任务</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-dropdown @command="(command) => handleSingleCopyTemplateCommand(command, scope.row.id)" trigger="click">
            <el-button type="text" size="small">
              复制首页通知<i class="el-icon-arrow-down"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-tooltip placement="right" effect="light">
                <div slot="content" style="max-width: 400px; white-space: pre-wrap;">
                  <strong>旧模板 (SMS_476755298)：</strong><br/>
                  尊敬的[专家姓名]老师，欢迎参加[会议名称]，请您打开下方链接填写您的基本信息，感谢您对本次会议的支持，祝您生活愉快。填写链接： xxxxxxx
                </div>
                <el-dropdown-item command="template1">旧模板</el-dropdown-item>
              </el-tooltip>
              <el-tooltip placement="right" effect="light">
                <div slot="content" style="max-width: 400px; white-space: pre-wrap;">
                  <strong>新模板 (SMS_489815559)：</strong><br/>
                  尊敬的[专家姓名]教授： 您好！兹定于[会议时间]于[会议地点]召开[会议名称]。请您打开下方链接查阅您的相关任务邀请和基本信息填写，衷心感谢您对本次会议的大力支持。填写链接： xxxxxxx 会务组：[系统配置] 期待您的莅临和支持，盼复！ 大会组委会。
                </div>
                <el-dropdown-item command="template2">新模板</el-dropdown-item>
              </el-tooltip>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="text" size="small" @click="activityconfigupdate(scope.row.id)">规则配置</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <showtask v-if="showtaskVisible" ref="showtask" @refreshDataList="getDataList"></showtask>
    <activity-guest-config v-if="configVisible" ref="guestConfig" @refreshDataList="getDataList">
    </activity-guest-config>

    <!-- 自定义导出资料选择对话框 -->
    <el-dialog title="选择要导出的专家资料" :visible.sync="exportDialogVisible" width="500px">
      <el-form label-width="120px">
        <el-form-item label="资料类型：">
          <el-checkbox-group v-model="selectedMaterialTypes" style="display: flex; flex-direction: column; gap: 10px;">
            <el-checkbox label="avatar" style="margin-right: 0;">
              <i class="el-icon-picture" style="margin-right: 5px;"></i>头像
            </el-checkbox>
            <el-checkbox label="introduction" style="margin-right: 0;">
              <i class="el-icon-document" style="margin-right: 5px;"></i>介绍文件
            </el-checkbox>
            <el-checkbox label="idCardZheng" style="margin-right: 0;">
              <i class="el-icon-postcard" style="margin-right: 5px;"></i>身份证正面
            </el-checkbox>
            <el-checkbox label="idCardFan" style="margin-right: 0;">
              <i class="el-icon-postcard" style="margin-right: 5px;"></i>身份证反面
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item>
          <el-alert title="提示" type="info" :closable="false" show-icon>
            <span slot="description">
              请至少选择一种资料类型进行导出。导出的文件将以ZIP压缩包的形式下载。
            </span>
          </el-alert>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmCustomExport" :disabled="selectedMaterialTypes.length === 0">
          <i class="el-icon-download" style="margin-right: 5px;"></i>确定导出
        </el-button>
      </div>
    </el-dialog>

    <!-- 新模板短信对话框 -->
    <el-dialog title="新模板短信确认" :visible.sync="newTemplateDialogVisible" width="600px" :close-on-click-modal="false">
      <el-form label-width="100px">
        <el-form-item>
          <el-alert
            title="说明"
            type="info"
            :closable="false"
            show-icon>
            <div slot="title">
              <strong>说明：</strong><br>
              会议时间、地点、内容、会务组信息将自动从系统配置中获取。
            </div>
          </el-alert>
        </el-form-item>
        <el-form-item>
          <el-alert
            title="短信模板预览"
            type="info"
            :closable="false"
            show-icon>
            <div slot="title">
              <strong>短信模板预览：</strong><br>
              尊敬的[专家姓名]教授： 您好！兹定于[会议时间]于[会议地点]召开[会议名称]。
              请您打开下方链接查阅您的相关任务邀请和基本信息填写，衷心感谢您对本次会议的大力支持。
              填写链接： xxxxxxx
              会务组：[系统配置] 期待您的莅临和支持，盼复！ 大会组委会。
            </div>
          </el-alert>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="newTemplateDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sendNewTemplateSms">确认发送</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AddOrUpdate from './activityguest-add-or-update'
import showtask from './activityguest-showtask'
import ActivityGuestConfig from './activityguest-config'
export default {
  data() {
    return {
      appid: '',
      dataForm: {
        name: '',
        mobile: '',
        isFirstChar: 0,
        activityId: undefined,
        taskStatus: ''
      },
      activityInfo: {},
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      showtaskVisible: false,
      configVisible: false,
      // 导出资料选择对话框
      exportDialogVisible: false,
      selectedMaterialTypes: [],
      // 新模板短信对话框
      newTemplateDialogVisible: false,
      currentSmsIds: []
    }
  },
  components: {
    AddOrUpdate,
    showtask,
    Upload: () => import('@/components/upload'),
    ActivityGuestConfig
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.dataForm.activityId = this.$route.query.activityId;
    this.getActivity();
  },
  methods: {
    searchList() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activityguest/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'mobile': this.dataForm.mobile,
          'activityId': this.dataForm.activityId,
          'isFirstChar': this.activityInfo.isFirstChar,
          'taskStatus': this.dataForm.taskStatus,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId, id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })

      // 先检查是否有日程任务
      this.$http({
        url: this.$http.adornUrl('/activity/activityguest/checkScheduleTasks'),
        method: 'post',
        data: this.$http.adornData(ids, false)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          const checkResult = data.data

          if (checkResult.hasScheduleTasks) {
            // 有日程任务，显示详细提醒
            const guestsWithTasks = checkResult.guestsWithTasks
            let warningMessage = '以下嘉宾在官网日程中还有任务，删除后将同时删除相关日程任务：\n\n'

            guestsWithTasks.forEach(guest => {
              warningMessage += `• ${guest.name}：${guest.taskTypes.join('、')}\n`
            })

            warningMessage += '\n确定要继续删除吗？'

            this.$confirm(warningMessage, '删除确认', {
              confirmButtonText: '确定删除',
              cancelButtonText: '取消',
              type: 'warning',
              dangerouslyUseHTMLString: false
            }).then(() => {
              this.performDelete(ids)
            })
          } else {
            // 没有日程任务，正常删除确认
            this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.performDelete(ids)
            })
          }
        } else {
          this.$message.error(data.msg || '检查日程任务失败')
        }
      }).catch(() => {
        this.$message.error('检查日程任务失败')
      })
    },

    // 执行删除操作
    performDelete(ids) {
      this.$http({
        url: this.$http.adornUrl('/activity/activityguest/delete'),
        method: 'post',
        data: this.$http.adornData(ids, false)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    sendTaskHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定[${id ? '发送学术通知短信' : '批量发送学术通知短信'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/sendTask'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '发送操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 处理短信模板选择
    handleSmsTemplateCommand(command) {
      if (command === 'template1') {
        // 旧模板，直接发送
        this.sendIndexHandle()
      } else if (command === 'template2') {
        // 新模板，显示对话框
        this.currentSmsIds = this.dataListSelections.map(item => item.id)
        this.newTemplateDialogVisible = true
      }
    },
    // 发送新模板短信
    sendNewTemplateSms() {
      const requestData = {
        ids: this.currentSmsIds,
        templateType: 2
      }

      this.$http({
        url: this.$http.adornUrl('/activity/activityguest/sendIndexWithTemplate'),
        method: 'post',
        data: this.$http.adornData(requestData, false)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message({
            message: '发送操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
          this.newTemplateDialogVisible = false
        } else {
          this.$message.error(data.msg)
        }
      })
    },

    // 处理单个专家短信模板选择
    handleSingleSmsTemplateCommand(command, id) {
      if (command === 'template1') {
        // 旧模板，直接发送
        this.sendIndexHandle(id)
      } else if (command === 'template2') {
        // 新模板，显示对话框
        this.currentSmsIds = [id]
        this.newTemplateDialogVisible = true
      }
    },

    // 处理单个专家复制模板选择
    handleSingleCopyTemplateCommand(command, id) {
      if (command === 'template1') {
        // 旧模板
        this.copyResult(id, 1)
      } else if (command === 'template2') {
        // 新模板
        this.copyResult(id, 2)
      }
    },

    // 处理批量复制模板选择
    handleBatchCopyTemplateCommand(command) {
      if (command === 'template1') {
        // 旧模板
        this.copyResult(null, 1)
      } else if (command === 'template2') {
        // 新模板
        this.copyResult(null, 2)
      }
    },
    sendIndexHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定[${id ? '发送专家通知短信' : '批量发送专家通知短信'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/sendIndex'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '发送操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    copyResult(id, templateType = 1) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })

      const requestData = {
        ids: ids,
        templateType: templateType
      }

      this.$http({
        url: this.$http.adornUrl('/activity/activityguest/copyIndexWithTemplate'),
        method: 'post',
        data: this.$http.adornData(requestData, false)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          const result = data.result;
          this.copyToClipboard(result);
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    copyToClipboard(text) {
      const input = document.createElement('input');
      input.setAttribute('value', text);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$message({
        message: '复制成功',
        type: 'success',
      });
    },
    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity;
          this.getDataList()
        }
      });
    },
    updateIsFirstChar(v) {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/updateIsFirstChar`),
        method: "post",
        data: this.$http.adornData({
          id: this.dataForm.activityId,
          isFirstChar: v,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message.success("操作成功");
          this.getActivity();
          this.pageIndex = 1;
          this.getDataList()
        } else {
          this.$message.error(data.msg)

        }
      });
    },
    showTaskHandle(v) {
      this.showtaskVisible = true
      this.$nextTick(() => {
        this.$refs.showtask.init(v)
      })
    },
    isImageUrl(url) {
      return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
    },
    // 导出
    exportHandle() {
      var url = this.$http.adornUrl("/activity/activityguest/export?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "name=" + this.dataForm.name,
        "mobile=" + this.dataForm.mobile,
        "activityId=" + this.dataForm.activityId,
        "isFirstChar=" + this.activityInfo.isFirstChar
      ].join('&'));
      window.open(url);
    },
    // 处理导出下拉菜单命令
    handleExportCommand(command) {
      switch (command) {
        case 'all':
          this.exportZipHandle(['avatar', 'introduction', 'idCardZheng', 'idCardFan']);
          break;
        case 'avatar':
          this.exportZipHandle(['avatar']);
          break;
        case 'introduction':
          this.exportZipHandle(['introduction']);
          break;
        case 'idCard':
          this.exportZipHandle(['idCardZheng', 'idCardFan']);
          break;
        case 'custom':
          this.showCustomExportDialog();
          break;
      }
    },

    // 处理导入下拉菜单命令
    handleImportCommand(command) {
      let matchType = '';
      switch (command) {
        case 'mobile':
          matchType = 'mobile';
          break;
        case 'name':
          matchType = 'name';
          break;
      }

      // 创建一个临时的文件输入元素
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = '.xlsx,.xls';
      fileInput.style.display = 'none';

      fileInput.onchange = (event) => {
        const file = event.target.files[0];
        if (file) {
          this.uploadImportFile(file, matchType);
        }
        document.body.removeChild(fileInput);
      };

      document.body.appendChild(fileInput);
      fileInput.click();
    },

    // 上传导入文件
    uploadImportFile(file, matchType) {
      const formData = new FormData();
      formData.append('file', file);

      const url = `/activity/activityguest/importExcel?activityId=${this.dataForm.activityId}&appid=${this.appid}&matchType=${matchType}`;

      this.$http({
        url: this.$http.adornUrl(url),
        responseType: 'arraybuffer',
        config: {
          withCredentials: false
        },
        method: 'post',
        data: formData
      }).then(({ data }) => {
        console.log(data);
        if (data.byteLength < 50) {
          this.$message.success("导入成功");
          this.getDataList();
        } else {
          this.$confirm(`存在导入失败数据，点击确定下载`, '提示', {
            confirmButtonText: '确定',
            showClose: false,
            type: 'error'
          }).then(() => {
            const blob = new Blob([data]);
            const fileName = `专家导入失败文件_${matchType === 'mobile' ? '按手机号匹配' : '按姓名匹配'}.xlsx`;
            const linkNode = document.createElement("a");
            linkNode.download = fileName;
            linkNode.style.display = "none";
            linkNode.href = URL.createObjectURL(blob);
            document.body.appendChild(linkNode);
            linkNode.click();
            URL.revokeObjectURL(linkNode.href);
            document.body.removeChild(linkNode);
          })
        }
      }).catch((error) => {
        console.error('导入失败:', error);
        this.$message.error('导入失败，请检查文件格式');
      });
    },

    // 显示自定义导出对话框
    showCustomExportDialog() {
      this.selectedMaterialTypes = [];
      this.exportDialogVisible = true;
    },

    // 确认自定义导出
    confirmCustomExport() {
      if (this.selectedMaterialTypes.length === 0) {
        this.$message.warning('请至少选择一种资料类型');
        return;
      }
      this.exportZipHandle(this.selectedMaterialTypes);
      this.exportDialogVisible = false;
    },

    // 修改后的导出方法，支持传入资料类型
    exportZipHandle(materialTypes = ['avatar', 'introduction', 'idCardZheng', 'idCardFan']) {
      var url = this.$http.adornUrl("/activity/activityguest/exportExpertMaterials?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "name=" + this.dataForm.name,
        "mobile=" + this.dataForm.mobile,
        "activityId=" + this.dataForm.activityId,
        "isFirstChar=" + this.activityInfo.isFirstChar,
        "materialTypes=" + materialTypes.join(',')
      ].join('&'));
      window.open(url);
    },
    downloadFile(e) {
      if (!e) {
        this.$message.error("未上传文件");
        return;
      }
      window.open(e);
    },
    activityguestplane() {
      this.$router.push({
        name: 'activityguestplane',
        query: {
          activityId: this.dataForm.activityId,
        },
      })
    },
    downloadDemo() {
      var url = this.$http.adornUrl("/activity/activityguest/downloadDemo?" + [
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    exportIndex() {
      var url = this.$http.adornUrl("/activity/activityguest/exportIndex?" + [
        "token=" + this.$cookie.get('token'),
        "activityId=" + this.dataForm.activityId,
      ].join('&'));
      window.open(url);
    },
    activityconfigupdate(id) {
      this.configVisible = true
      this.$nextTick(() => {
        this.$refs.guestConfig.init(id)
      })
    },
    activityconfigupdateTurn() {
      this.$router.push({
        name: 'activityconfigupdate',
        query: {
          activityId: this.dataForm.activityId,
        },
      })

    },
  }
}
</script>
