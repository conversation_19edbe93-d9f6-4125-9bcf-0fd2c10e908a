<template>
    <div>
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
            label-width="140px">

            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        确定议程任务</div>
                    <el-col :span="8">
                        <el-form-item label="开启议程任务" prop="guestSchedule">
                            <el-select v-model="dataForm.guestSchedule" placeholder="开启议程任务" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="确定议程任务时间" prop="guestScheduleStart">
                            <el-date-picker v-model="times1" @change="guestScheduleStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        确定专家基本信息</div>
                    <el-col :span="8">
                        <el-form-item label="开启确定专家信息" prop="guestInfo">
                            <el-select v-model="dataForm.guestInfo" placeholder="开启确定专家信息" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="确定专家信息时间" prop="guestInfoStart">
                            <el-date-picker v-model="times2" @change="guestInfoStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        专家行程</div>
                    <el-col :span="8">
                        <el-form-item label="开启专家行程" prop="guestTrip">
                            <el-select v-model="dataForm.guestTrip" placeholder="开启专家行程" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="专家行程时间" prop="guestTripStart">
                            <el-date-picker v-model="times3" @change="guestTripStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        劳务费信息收集</div>
                    <el-col :span="8">
                        <el-form-item label="劳务费信息收集" prop="guestServiceInfo">
                            <el-select v-model="dataForm.guestServiceInfo" placeholder="劳务费信息收集" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="劳务费信息收集时间" prop="guestServiceInfoStart">
                            <el-date-picker v-model="times5" @change="guestServiceInfoStartdateChange"
                                type="datetimerange" value-format="yyyy/MM/dd HH:mm:ss" range-separator="至"
                                start-placeholder="开始时间" end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        劳务费签字确认</div>
                    <el-col :span="8">
                        <el-form-item label="劳务费签字确认" prop="guestService">
                            <el-select v-model="dataForm.guestService" placeholder="劳务费签字确认" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="劳务费签字确认时间" prop="guestServiceStart">
                            <el-date-picker v-model="times4" @change="guestServiceStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="隐藏劳务费" prop="hiddenServicefee">
                            <el-select v-model="dataForm.hiddenServicefee" placeholder="隐藏劳务费" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>

            <!-- 劳务报酬签收单模板配置 -->
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        劳务报酬签收单模板配置</div>

                    <el-col :span="24">
                        <el-form-item label="启用自定义模板" prop="enableCustomTemplate">
                            <el-switch
                                v-model="dataForm.enableCustomTemplate"
                                :active-value="1"
                                :inactive-value="0"
                                active-text="启用"
                                inactive-text="禁用"
                                @change="onTemplateEnableChange">
                            </el-switch>
                            <span style="margin-left: 15px; color: #909399; font-size: 12px;">
                                启用后可以自定义劳务报酬签收单的内容和格式
                            </span>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24" v-if="dataForm.enableCustomTemplate === 1">
                        <el-form-item label="模板内容">
                            <div style="margin-bottom: 10px;">
                                <span style="color: #666; margin-right: 10px;">快速插入：</span>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{姓名}')">姓名</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{单位}')">单位</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{银行账户}')">银行账户</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{开户行}')">开户行</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{省份}')">省份</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{城市}')">城市</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{证件类型}')">证件类型</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{证件号}')">证件号</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{手机号码}')">手机号码</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{劳务费}')">劳务费</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{会议名称}')">会议名称</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{开始时间}')">开始时间</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{协会}')">协会</el-button>
                                <el-button size="mini" type="text" @click="insertPlaceholder('{签名}')">签名</el-button>
                            </div>

                            <!-- 富文本编辑器 -->
                            <el-input
                                type="textarea"
                                v-model="dataForm.guestFeeTemplate"
                                :rows="15"
                                placeholder="请输入劳务报酬签收单模板内容，可以使用上方按钮插入动态字段">
                            </el-input>

                            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
                                <p><strong>使用说明：</strong></p>
                                <p>1. 点击上方按钮可以插入动态字段占位符，生成PDF时会自动替换为实际数据</p>
                                <p>2. 可以自由编辑文字内容，支持HTML格式（如 &lt;b&gt;粗体&lt;/b&gt;、&lt;br&gt;换行等）</p>
                                <p>3. 支持表格格式，可以手动输入HTML表格代码</p>
                            </div>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24" v-if="dataForm.enableCustomTemplate === 1">
                        <el-form-item>
                            <el-button type="primary" @click="saveTemplate">保存模板</el-button>
                            <el-button type="success" @click="previewTemplate">预览效果</el-button>
                            <el-button type="info" @click="loadDefaultTemplate">加载默认模板</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>

            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="12">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        确认是否接送</div>
                    <el-col :span="8">
                        <el-form-item label="确认是否接送" prop="guestLink">
                            <el-select v-model="dataForm.guestLink" placeholder="确认是否接送" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="确认是否接送时间" prop="guestLinkStart">
                            <el-date-picker v-model="times6" @change="guestLinkStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="接默认地点" prop="linkStart">
                            <el-input v-model="dataForm.linkStart" placeholder="接默认地点" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="送默认地点" prop="linkEnd">
                            <el-input v-model="dataForm.linkEnd" placeholder="送默认地点" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        其他配置</div>
                    <el-col :span="12">
                        <el-form-item label="所属协会" prop="association">
                            <el-input v-model="dataForm.association" placeholder="所属协会" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="会议联系人" prop="contact">
                            <el-input v-model="dataForm.contact" placeholder="会议联系人" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系方式" prop="mobile">
                            <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="会议联系人微信" prop="qrcode">
                            <el-upload class="avatar-uploader" list-type="picture-card" :before-upload="checkFileSize"
                                :show-file-list="false" accept=".jpg, .jpeg, .png, .gif"
                                :on-success="qrcodeSuccessHandle" :action="url">
                                <img width="100px" v-if="dataForm.qrcode" :src="dataForm.qrcode" class="avatar">
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        专家行程配置</div>
                        
                        <el-col :span="8">
                        <el-form-item label="飞机出票规则" prop="planeTicketType">
                            <el-select v-model="dataForm.planeTicketType" placeholder="飞机出票规则" filterable>
                                <el-option v-for="item in ticketType" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="火车出票规则" prop="trainTicketType">
                            <el-select v-model="dataForm.trainTicketType" placeholder="火车出票规则" filterable>
                                <el-option v-for="item in ticketType" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>

            <!-- 酒店预定配置 -->
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        确定酒店预定</div>
                    <el-col :span="8">
                        <el-form-item label="开启酒店预定" prop="collectHotel">
                            <el-select v-model="dataForm.collectHotel" placeholder="开启酒店预定" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="酒店预定功能开放时间" prop="guestHotelStart">
                            <el-date-picker v-model="times7" @change="guestHotelStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 酒店预定时间区间配置 -->
                <el-row v-if="dataForm.collectHotel == 1" :gutter="24" style="margin-top: 20px;">
                    <el-col :span="24">
                        <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; border: 1px solid #bae7ff;">
                            <h4 style="margin: 0 0 15px 0; color: #1890ff;">
                                <i class="el-icon-date"></i> 酒店预定时间区间配置
                            </h4>
                            <el-row :gutter="24">
                                <el-col :span="24">
                                    <el-form-item label="酒店预定时间区间" prop="hotelDateRange">
                                        <el-date-picker
                                            v-model="dataForm.hotelDateRange"
                                            type="daterange"
                                            range-separator="至"
                                            start-placeholder="入住日期"
                                            end-placeholder="退房日期"
                                            format="yyyy/MM/dd"
                                            value-format="yyyy/MM/dd"
                                            style="width: 100%;">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-col>
                </el-row>
            </el-card>

            <!-- 嘉宾信息收集配置 -->
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        嘉宾信息收集配置</div>

                    <!-- 身份证号码 -->
                    <el-col :span="6">
                        <el-form-item label="身份证号码" prop="collectIdCardNumber">
                            <el-switch
                                v-model="dataForm.collectIdCardNumber"
                                :active-value="1"
                                :inactive-value="0"
                                active-text="开启"
                                inactive-text="关闭">
                            </el-switch>
                        </el-form-item>
                    </el-col>

                    <!-- 身份证正反面 -->
                    <el-col :span="6">
                        <el-form-item label="身份证正反面" prop="collectIdCard">
                            <el-switch
                                v-model="dataForm.collectIdCard"
                                :active-value="1"
                                :inactive-value="0"
                                active-text="开启"
                                inactive-text="关闭">
                            </el-switch>
                        </el-form-item>
                    </el-col>

                    <!-- 地区信息 -->
                    <el-col :span="6">
                        <el-form-item label="地区信息" prop="collectRegion">
                            <el-switch
                                v-model="dataForm.collectRegion"
                                :active-value="1"
                                :inactive-value="0"
                                active-text="开启"
                                inactive-text="关闭">
                            </el-switch>
                        </el-form-item>
                    </el-col>

                    <!-- 银行卡号 -->
                    <el-col :span="6">
                        <el-form-item label="银行卡号" prop="collectBankCard">
                            <el-switch
                                v-model="dataForm.collectBankCard"
                                :active-value="1"
                                :inactive-value="0"
                                active-text="开启"
                                inactive-text="关闭">
                            </el-switch>
                        </el-form-item>
                    </el-col>

                    <!-- 开户行 -->
                    <el-col :span="6">
                        <el-form-item label="开户行" prop="collectBankName">
                            <el-switch
                                v-model="dataForm.collectBankName"
                                :active-value="1"
                                :inactive-value="0"
                                active-text="开启"
                                inactive-text="关闭">
                            </el-switch>
                        </el-form-item>
                    </el-col>


                </el-row>



            </el-card>
        </el-form>
        <div style="text-align: center;">
            <el-button @click="$router.go(-1)">返回</el-button>
            <el-button type="primary" @click="dataFormSubmit()" :loading="loading">确定</el-button>
        </div>
    </div>
</template>

<script>
import { yesOrNo } from "@/data/common";
import { ticketType } from "@/data/activity";
export default {
    data() {
        return {
        appid: '',
            url: '',
            times1: [],
            times2: [],
            times3: [],
            times4: [],
            times5: [],
            times6: [],
            times7: [],
            ticketType,
            yesOrNo,
            dataForm: {
                repeatToken: '',
                id: '',
                activityId: '',
                guestScheduleStart: '',
                guestScheduleEnd: '',
                guestSchedule: 0,
                guestInfoStart: '',
                guestInfoEnd: '',
                guestInfo: 0,
                guestTripStart: '',
                guestTripEnd: '',
                guestTrip: 0,
                guestServiceStart: '',
                guestServiceEnd: '',
                guestService: 0,
                guestServiceInfoStart: '',
                guestServiceInfoEnd: '',
                guestServiceInfo: 0,
                guestLinkStart: '',
                guestLinkEnd: '',
                guestLink: 0,
                planeTicketType: 0,
                trainTicketType: 0,
                contact: '',
                mobile: '',
                qrcode: '',
                association: '',
                linkStart: '',
                linkEnd: '',
                hiddenServicefee: 0,
                // 嘉宾信息收集配置 - 默认都开启
                collectIdCardNumber: 1, // 身份证号码
                collectIdCard: 1,      // 身份证正反面
                collectRegion: 1,      // 地区信息
                collectBankCard: 1,    // 银行卡号
                collectBankName: 1,    // 开户行
                collectHotel: 1,       // 酒店预定
                hotelDateRange: [],    // 酒店预定时间区间 [入住日期, 退房日期]
                guestHotelStart: '',   // 酒店预定功能开始时间
                guestHotelEnd: '',     // 酒店预定功能结束时间
                // 劳务报酬签收单模板配置
                enableCustomTemplate: 0, // 是否启用自定义模板
                guestFeeTemplate: ''     // 模板内容
            },
            dataRule: {
                // startCertDate: [
                //     { required: true, message: "期初建账时间不能为空", trigger: "blur" },
                // ],
            },
        };
    },
    mounted() {
        this.appid = this.$cookie.get("appid")
      this.url = this.$http.adornUrl(
        `/sys/oss/upload?token=${this.$cookie.get("token")}`
      );
        this.dataForm.activityId = this.$route.query.activityId
        this.getToken();
        this.init();
    },
    methods: {
        guestScheduleStartdateChange(v) {
            this.dataForm.guestScheduleStart = v[0];
            this.dataForm.guestScheduleEnd = v[1];
        },
        guestInfoStartdateChange(v) {
            this.dataForm.guestInfoStart = v[0];
            this.dataForm.guestInfoEnd = v[1];
        },
        guestTripStartdateChange(v) {
            this.dataForm.guestTripStart = v[0];
            this.dataForm.guestTripEnd = v[1];
        },
        guestServiceStartdateChange(v) {
            this.dataForm.guestServiceStart = v[0];
            this.dataForm.guestServiceEnd = v[1];
        },
        guestServiceInfoStartdateChange(v) {
            this.dataForm.guestServiceInfoStart = v[0];
            this.dataForm.guestServiceInfoEnd = v[1];
        },
        guestLinkStartdateChange(v) {
            this.dataForm.guestLinkStart = v[0];
            this.dataForm.guestLinkEnd = v[1];
        },
        guestHotelStartdateChange(v) {
            this.dataForm.guestHotelStart = v[0];
            this.dataForm.guestHotelEnd = v[1];
        },
        init() {
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields()
                this.$http({
                    url: this.$http.adornUrl(
                        `/activity/activityconfig/findByActivityId/${this.dataForm.activityId}`
                    ),
                    method: "get",
                    params: this.$http.adornParams(),
                }).then(({ data }) => {
                    if (data && data.code === 200) {
                        this.dataForm.id = data.result.id
                        this.dataForm.activityId = data.result.activityId
                        this.dataForm.guestScheduleStart = data.result.guestScheduleStart
                        this.dataForm.guestScheduleEnd = data.result.guestScheduleEnd
                        this.times1 = this.dataForm.guestScheduleStart ? [
                            data.result.guestScheduleStart,
                            data.result.guestScheduleEnd,
                        ] : [];
                        this.dataForm.guestSchedule = data.result.guestSchedule
                        this.dataForm.guestInfoStart = data.result.guestInfoStart
                        this.dataForm.guestInfoEnd = data.result.guestInfoEnd
                        this.times2 = this.dataForm.guestInfoStart ? [
                            data.result.guestInfoStart,
                            data.result.guestInfoEnd,
                        ] : [];
                        this.dataForm.guestInfo = data.result.guestInfo
                        this.dataForm.guestTripStart = data.result.guestTripStart
                        this.dataForm.guestTripEnd = data.result.guestTripEnd
                        this.times3 = this.dataForm.guestTripStart ? [
                            data.result.guestTripStart,
                            data.result.guestTripEnd,
                        ] : [];
                        this.dataForm.guestTrip = data.result.guestTrip
                        this.dataForm.guestServiceStart = data.result.guestServiceStart
                        this.dataForm.guestServiceEnd = data.result.guestServiceEnd
                        this.times4 = this.dataForm.guestServiceStart ? [
                            data.result.guestServiceStart,
                            data.result.guestServiceEnd,
                        ] : [];
                        this.dataForm.guestService = data.result.guestService
                        this.dataForm.guestServiceInfoStart = data.result.guestServiceInfoStart
                        this.dataForm.guestServiceInfoEnd = data.result.guestServiceInfoEnd
                        this.times5 = this.dataForm.guestServiceInfoStart ? [
                            data.result.guestServiceInfoStart,
                            data.result.guestServiceInfoEnd,
                        ] : [];
                        this.dataForm.guestLinkStart = data.result.guestLinkStart
                        this.dataForm.guestLinkEnd = data.result.guestLinkEnd
                        this.times6 = this.dataForm.guestLinkStart ? [
                            data.result.guestLinkStart,
                            data.result.guestLinkEnd,
                        ] : [];
                        this.dataForm.guestLink = data.result.guestLink

                        // 处理酒店预定功能开放时间
                        this.dataForm.guestHotelStart = data.result.guestHotelStart
                        this.dataForm.guestHotelEnd = data.result.guestHotelEnd
                        this.times7 = this.dataForm.guestHotelStart ? [
                            data.result.guestHotelStart,
                            data.result.guestHotelEnd,
                        ] : [];
                        this.dataForm.association = data.result.association
                        this.dataForm.contact = data.result.contact
                        this.dataForm.mobile = data.result.mobile
                        this.dataForm.qrcode = data.result.qrcode
                        this.dataForm.planeTicketType = data.result.planeTicketType
                        this.dataForm.trainTicketType = data.result.trainTicketType
                        this.dataForm.linkStart = data.result.linkStart
                        this.dataForm.linkEnd = data.result.linkEnd
                        this.dataForm.hiddenServicefee = data.result.hiddenServicefee

                        // 嘉宾信息收集配置
                        this.dataForm.collectIdCardNumber = data.result.collectIdCardNumber 
                        this.dataForm.collectIdCard = data.result.collectIdCard 
                        this.dataForm.collectRegion = data.result.collectRegion 
                        this.dataForm.collectBankCard = data.result.collectBankCard 
                        this.dataForm.collectBankName = data.result.collectBankName 
                        this.dataForm.collectHotel = data.result.collectHotel 

                        // 处理酒店日期区间
                        if (data.result.hotelCheckInTime && data.result.hotelCheckOutTime) {
                            this.dataForm.hotelDateRange = [data.result.hotelCheckInTime,data.result.hotelCheckOutTime];
                        } else {
                            this.dataForm.hotelDateRange = [];
                        }

                        // 处理酒店预定功能开放时间区间
                        if (data.result.guestHotelStart && data.result.guestHotelEnd) {
                            this.dataForm.guestHotelTimeRange = [data.result.guestHotelStart, data.result.guestHotelEnd];
                        } else {
                            this.dataForm.guestHotelTimeRange = [];
                        }

                        // 模板配置
                        this.dataForm.enableCustomTemplate = data.result.enableCustomTemplate || 0
                        this.dataForm.guestFeeTemplate = data.result.guestFeeTemplate || ''

                        // 如果启用了自定义模板但没有模板内容，自动加载默认模板
                        if (this.dataForm.enableCustomTemplate === 1 && !this.dataForm.guestFeeTemplate) {
                            this.loadDefaultTemplateQuietly()
                        }
                    }
                })
            });
        }, getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                .then(({ data }) => {
                    if (data && data.code === 200) {
                        this.dataForm.repeatToken = data.result;
                    }
                })
        },
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 / 1024 > 6) {
        this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
        return false
      }
      if (file.size / 1024 > 1000) {
        // 100kb不压缩
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.8,
            success(result) {
              resolve(result)
            },
            error(error) {
                console.log(error)
            }
          })
        })
      }
      return true
    },
    beforeUploadHandle(file) {
      if (
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "image/png" &&
        file.type !== "image/gif"
      ) {
        this.$message.error("只支持jpg、png、gif格式的图片！");
        return false;
      }
    },
    // 上传成功（背景）
    qrcodeSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.qrcode = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
        // 表单提交
        dataFormSubmit() {
            this.$refs["dataForm"].validate((valid) => {
                if (valid) {
                    this.loading = true;
                    this.$http({
                        url: this.$http.adornUrl(`/activity/activityconfig/update`),
                        method: 'post',
                        data: this.$http.adornData({
                            'repeatToken': this.dataForm.repeatToken,
                            'id': this.dataForm.id,
                            'activityId': this.dataForm.activityId,
                            'guestScheduleStart': this.dataForm.guestScheduleStart,
                            'guestScheduleEnd': this.dataForm.guestScheduleEnd,
                            'guestSchedule': this.dataForm.guestSchedule,
                            'guestInfoStart': this.dataForm.guestInfoStart,
                            'guestInfoEnd': this.dataForm.guestInfoEnd,
                            'guestInfo': this.dataForm.guestInfo,
                            'guestTripStart': this.dataForm.guestTripStart,
                            'guestTripEnd': this.dataForm.guestTripEnd,
                            'guestTrip': this.dataForm.guestTrip,
                            'guestServiceStart': this.dataForm.guestServiceStart,
                            'guestServiceEnd': this.dataForm.guestServiceEnd,
                            'guestService': this.dataForm.guestService,
                            'guestServiceInfoStart': this.dataForm.guestServiceInfoStart,
                            'guestServiceInfoEnd': this.dataForm.guestServiceInfoEnd,
                            'guestServiceInfo': this.dataForm.guestServiceInfo,
                            'guestLinkStart': this.dataForm.guestLinkStart,
                            'guestLinkEnd': this.dataForm.guestLinkEnd,
                            'guestLink': this.dataForm.guestLink,
                            'guestHotelStart': this.dataForm.guestHotelStart,
                            'guestHotelEnd': this.dataForm.guestHotelEnd,
                            'association': this.dataForm.association,
                            'contact': this.dataForm.contact,
                            'mobile': this.dataForm.mobile,
                            'qrcode': this.dataForm.qrcode,
                            'planeTicketType': this.dataForm.planeTicketType,
                            'trainTicketType': this.dataForm.trainTicketType,
                            'linkStart': this.dataForm.linkStart,
                            'linkEnd': this.dataForm.linkEnd,
                            'hiddenServicefee': this.dataForm.hiddenServicefee,
                            // 嘉宾信息收集配置
                            'collectIdCardNumber': this.dataForm.collectIdCardNumber,
                            'collectIdCard': this.dataForm.collectIdCard,
                            'collectRegion': this.dataForm.collectRegion,
                            'collectBankCard': this.dataForm.collectBankCard,
                            'collectBankName': this.dataForm.collectBankName,
                            'collectHotel': this.dataForm.collectHotel,
                            // 酒店日期区间处理
                            'hotelCheckInTime': this.dataForm.hotelDateRange && this.dataForm.hotelDateRange.length > 0
                                ? this.dataForm.hotelDateRange[0] : '',
                            'hotelCheckOutTime': this.dataForm.hotelDateRange && this.dataForm.hotelDateRange.length > 1
                                ? this.dataForm.hotelDateRange[1] : '',
                            // 酒店预定功能开放时间处理
                            'guestHotelStart': this.dataForm.guestHotelTimeRange && this.dataForm.guestHotelTimeRange.length > 0
                                ? this.dataForm.guestHotelTimeRange[0] : '',
                            'guestHotelEnd': this.dataForm.guestHotelTimeRange && this.dataForm.guestHotelTimeRange.length > 1
                                ? this.dataForm.guestHotelTimeRange[1] : '',
                            // 模板配置
                            'enableCustomTemplate': this.dataForm.enableCustomTemplate,
                            'guestFeeTemplate': this.dataForm.guestFeeTemplate,
                        }),
                    }).then(({ data }) => {
                        if (data && data.code === 200) {
                            this.$message({
                                message: "操作成功",
                                type: "success",
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false;
                                    this.$emit("refreshDataList");
                                },
                            });
                        } else {
                            this.$message.error(data.msg);
                            if (data.msg != '不能重复提交') {
                                this.getToken();
                            }
                        }
                        this.loading = false;
                    });
                }
            });
        },

        // 格式化日期为 yyyy/MM/dd 格式
        formatDateToYMD(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}/${month}/${day}`;
        },

        // 将 yyyy/MM/dd 格式转换为日期字符串
        formatYMDToDate(ymdStr) {
            if (!ymdStr) return '';
            return ymdStr.replace(/\//g, '-');
        },

        // 模板配置相关方法

        // 模板开关变化处理
        onTemplateEnableChange(value) {
            if (value === 1) {
                // 启用自定义模板时，如果没有模板内容，自动加载默认模板
                if (!this.dataForm.guestFeeTemplate) {
                    this.loadDefaultTemplateQuietly();
                }
            }
        },

        // 插入占位符
        insertPlaceholder(placeholder) {
            // 获取当前光标位置
            const textarea = document.querySelector('textarea[placeholder*="劳务报酬签收单模板"]');
            if (textarea) {
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const text = this.dataForm.guestFeeTemplate;

                // 在光标位置插入占位符
                this.dataForm.guestFeeTemplate = text.substring(0, start) + placeholder + text.substring(end);

                // 重新设置光标位置
                this.$nextTick(() => {
                    textarea.focus();
                    textarea.setSelectionRange(start + placeholder.length, start + placeholder.length);
                });
            } else {
                // 如果没有找到textarea，直接在末尾添加
                this.dataForm.guestFeeTemplate += placeholder;
            }
        },

        // 保存模板
        saveTemplate() {
            if (!this.dataForm.guestFeeTemplate.trim()) {
                this.$message.warning('请输入模板内容');
                return;
            }

            // 这里可以单独保存模板，或者提示用户保存整个配置
            this.$message.success('模板内容已更新，请点击页面底部的"确定"按钮保存配置');
        },

        // 预览模板
        previewTemplate() {
            if (!this.dataForm.guestFeeTemplate.trim()) {
                this.$message.warning('请先输入模板内容');
                return;
            }

            // 模拟数据替换
            let previewContent = this.dataForm.guestFeeTemplate;
            const mockData = {
                '{姓名}': '张三教授',
                '{单位}': '某某医科大学附属医院',
                '{银行账户}': '6222021234567890123',
                '{开户行}': '中国银行某某支行',
                '{省份}': '福建省',
                '{城市}': '福州市',
                '{证件类型}': '身份证',
                '{证件号}': '350100199001011234',
                '{手机号码}': '13800138000',
                '{劳务费}': '5,000.00',
                '{会议名称}': '2025年全国医学学术交流会',
                '{开始时间}': '2025年1月17日',
                '{协会}': '中华医学会',
                '{签名}': '_______________'
            };

            // 替换占位符
            Object.keys(mockData).forEach(key => {
                previewContent = previewContent.replace(new RegExp(key.replace(/[{}]/g, '\\$&'), 'g'), mockData[key]);
            });

            // 显示预览
            this.$alert(previewContent, '模板预览', {
                dangerouslyUseHTMLString: true,
                customClass: 'template-preview-dialog'
            });
        },

        // 加载默认模板
        loadDefaultTemplate() {
            this.dataForm.guestFeeTemplate = this.getCompleteDefaultTemplate();
            this.$message.success('已加载默认模板');
        },

        // 静默加载默认模板（不显示提示消息）
        loadDefaultTemplateQuietly() {
            // 直接使用本地完整的默认模板，不依赖后端API
            this.dataForm.guestFeeTemplate = this.getCompleteDefaultTemplate();
        },

        // 获取完整的默认模板
        getCompleteDefaultTemplate() {
            return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>劳务报酬签收单</title>
    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: SimHei;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .content {
            margin-bottom: 20px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table td {
            border: 1px solid #000;
            padding: 10px;
            text-align: center;
            font-weight: 600;
            height: 40px;
        }
        .signature-section {
            margin-top: 30px;
        }
        .signature-table {
            width: 100%;
            border: none !important;
            border-collapse: collapse;
        }
        .signature-table td {
            border: none !important;
            padding: 10px;
            text-align: left;
            vertical-align: top;
            height: 60px;
            line-height: 60px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>劳务报酬签收单</h2>
    </div>

    <div class="content">
        <div>尊敬的{姓名}主任：</div>
    </div>

    <div class="content">
        <div style="line-height: 1.5;">{协会}(以下简称协会)感谢您接受邀请，并在{开始时间}的"{会议名称}"提供相关会议服务:讲课/讨论/主持等。为此，我协会向您支付劳务报酬人民币(税后)<strong>{劳务费}</strong>元，相关的个人所得税将由我协会为您代扣代缴。</div>
    </div>

    <div class="content">
        <div style="text-indent: 2em;line-height: 1.5;margin-bottom: 20px">请根据以下要求填写费用支付相关信息，我协会将按照以下信息进行本次服务费用的支付。</div>
    </div>

    <table class="info-table">
        <tbody>
            <tr>
                <td style="width: 20%">姓名</td>
                <td style="width: 30%">{姓名}</td>
                <td style="width: 20%">单位</td>
                <td style="width: 30%">{单位}</td>
            </tr>
            <tr>
                <td style="width: 20%">银行账户</td>
                <td style="width: 30%">{银行账户}</td>
                <td style="width: 20%">开户行</td>
                <td style="width: 30%">{开户行}</td>
            </tr>
            <tr>
                <td style="width: 20%">省</td>
                <td style="width: 30%">{省份}</td>
                <td style="width: 20%">市</td>
                <td style="width: 30%">{城市}</td>
            </tr>
            <tr>
                <td style="width: 20%">证件类型</td>
                <td style="width: 30%">{证件类型}</td>
                <td style="width: 20%">证件号</td>
                <td style="width: 30%">{证件号}</td>
            </tr>
        </tbody>
    </table>

    <div class="content">
        <div style="text-indent: 2em;line-height: 1.5;margin-top: 20px">您本次讲课的内容、资料(包括相关信息)和您的个人信息，我协会已全部收集并保存。</div>
    </div>

    <div class="signature-section">
        <table class="signature-table" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td style="width: 50%; border: none !important;">手机号码：{手机号码}</td>
                <td style="width: 50%; border: none !important;">在此签名：{签名}</td>
            </tr>
        </table>
    </div>
</body>
</html>`;
        }
    },
};
</script>

<style scoped>
/* 模板预览对话框样式 */
.template-preview-dialog >>> .el-message-box {
  width: 80%;
  max-width: 800px;
}

.template-preview-dialog >>> .el-message-box__content {
  max-height: 500px;
  overflow-y: auto;
}

.template-preview-dialog >>> table {
  border-collapse: collapse;
  width: 100%;
}

.template-preview-dialog >>> td,
.template-preview-dialog >>> th {
  border: 1px solid #000;
  padding: 8px;
  text-align: center;
}
</style>
