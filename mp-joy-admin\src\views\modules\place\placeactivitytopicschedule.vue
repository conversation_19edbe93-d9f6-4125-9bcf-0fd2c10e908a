<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-select v-model="dataForm.placeId" placeholder="全部场地" filterable>
          <el-option label="全部场地" value=""></el-option>
          <el-option v-for="item in placeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.topicId" placeholder="全部主题" filterable>
          <el-option label="全部主题" value=""></el-option>
          <el-option v-for="item in placeTopicList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="日程名称" clearable @input="debouncedSearch"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="searchList()">查询</el-button>
        <el-button v-if="isAuth('place:placeactivitytopicschedule:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
          
        <el-button @click="downloadDemo()" type="success">导入模板</el-button>
        <el-button type="primary">
          <Upload @uploaded="getDataList" :url="'/place/placeactivitytopicschedule/importExcel?appid=' + appid + '&activityId=' + dataForm.activityId" :name="'日程导入'"></Upload>
        </el-button>
        <el-button v-if="isAuth('place:placeactivitytopicschedule:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <!-- <el-button type="success" @click="syncAllTopicTimes()"
                   :disabled="dataList.length === 0"
                   title="同步所有主题的日程时间，使其连贯">
          <i class="el-icon-refresh"></i> 全部时间同步
        </el-button> -->
      </el-form-item>
      <el-form-item >
        <el-dropdown @command="handleDropdownCommand">
          <el-button type="warning">
            更多操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="placeImport" v-if="appid != 'wx0770d56458b33c67'">选择场地导入</el-dropdown-item>
            <el-dropdown-item command="exportWord">导出Word</el-dropdown-item>
            <el-dropdown-item command="exportWordPageBreak">导出Word(主题分页)</el-dropdown-item>
            <el-dropdown-item command="exportExcel">导出Excel</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown></el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <!-- 主题分组的日程列表 -->
    <div v-loading="dataListLoading" class="topic-schedule-container">
      <div v-if="dataList.length === 0" class="empty-state">
        <el-empty description="暂无数据"></el-empty>
      </div>

      <div v-else>
        <div v-for="topic in dataList" :key="topic.id" class="topic-group">
          <!-- 主题标题栏 -->
          <div class="topic-header">
            <div class="topic-info">
              <h3 class="topic-title">{{ topic.name }}</h3>
              <div class="topic-meta">
                <span class="place-name">{{ topic.placeName }}</span>
                <span class="topic-time">{{ formatTopicTime(topic.startTime, topic.endTime) }}</span>
                <span class="schedule-count">{{ (topic.placeActivityTopicScheduleEntities || []).length }}个日程</span>
              </div>
            </div>
            <div class="topic-actions">
              <el-button size="small" type="primary" @click="addScheduleToTopic(topic.id, 0)">
                <i class="el-icon-plus"></i> 添加日程
              </el-button>
              <el-button size="small" type="success" @click="syncTopicScheduleTimes(topic)"
                         :disabled="!topic.placeActivityTopicScheduleEntities || topic.placeActivityTopicScheduleEntities.length < 2"
                         title="让该主题下的所有日程时间连贯起来">
                <i class="el-icon-refresh"></i> 时间同步
              </el-button>
            </div>
          </div>

          <!-- 日程列表 -->
          <div class="schedules-container">
            <div v-if="!topic.placeActivityTopicScheduleEntities || topic.placeActivityTopicScheduleEntities.length === 0"
                 class="no-schedules">
              <p>该主题暂无日程</p>
            </div>

            <div v-else class="schedule-list">
              <div v-for="(schedule, index) in topic.placeActivityTopicScheduleEntities" :key="`schedule-${schedule.id}`">
                <!-- 插入位置指示器 -->
                <div class="insert-indicator" @click="addScheduleToTopic(topic.id, index)"
                     :title="getInsertHint(topic, index)">
                  <i class="el-icon-plus"></i>
                  <span>在此处插入日程</span>
                  <span class="insert-hint">{{ getInsertTimeHint(topic, index) }}</span>
                </div>

                <!-- 日程卡片 -->
                <div class="schedule-card">
                  <div class="schedule-content">
                    <div class="schedule-header">
                      <div class="schedule-title">{{ schedule.name }}</div>
                      <div class="schedule-meta">
                        <span class="schedule-time">{{ formatScheduleTime(schedule.startTime, schedule.endTime) }}</span>
                        <span class="schedule-duration">({{ calculateDuration(schedule.startTime, schedule.endTime) }}分钟)</span>
                      </div>
                    </div>

                    <div class="schedule-participants">
                      <div v-if="schedule.activitySpeakers && schedule.activitySpeakers.length > 0" class="participant-row">
                        <span class="participant-label">{{ getLabelName('主持', schedule.aliasSpeakerName) }}:</span>
                        <div class="participant-tags">
                          <el-tag v-for="speaker in schedule.activitySpeakers" :key="speaker.id"
                                  size="mini" type="primary"
                                  :class="'tag-color tag-color-' + (speaker.confirmStatus || 0)"
                                  @click="showSpeaker(schedule.id)">
                            {{ speaker.name }}
                          </el-tag>
                        </div>
                      </div>

                      <div v-if="schedule.activityGuests && schedule.activityGuests.length > 0" class="participant-row">
                        <span class="participant-label">{{ getLabelName('讲者', schedule.aliasGuestName) }}:</span>
                        <div class="participant-tags">
                          <el-tag v-for="guest in schedule.activityGuests" :key="guest.id"
                                  size="mini" type="success"
                                  :class="'tag-color tag-color-' + (guest.confirmStatus || 0)"
                                  @click="showGuest(schedule.id)">
                            {{ guest.name }}
                          </el-tag>
                        </div>
                      </div>

                      <div v-if="schedule.activityDiscuss && schedule.activityDiscuss.length > 0" class="participant-row">
                        <span class="participant-label">{{ getLabelName('讨论', schedule.aliasDiscussName) }}:</span>
                        <div class="participant-tags">
                          <el-tag v-for="discuss in schedule.activityDiscuss" :key="discuss.id"
                                  size="mini" type="warning"
                                  :class="'tag-color tag-color-' + (discuss.confirmStatus || 0)"
                                  @click="showDiscuss(schedule.id)">
                            {{ discuss.name }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="schedule-actions">
                    <div class="move-buttons">
                      <el-button type="text" size="mini" :disabled="index === 0" @click="moveSchedule(topic.id, schedule.id, 'up')" title="上移">
                        <i class="el-icon-arrow-up"></i>
                      </el-button>
                      <el-button type="text" size="mini" :disabled="index === topic.placeActivityTopicScheduleEntities.length - 1" @click="moveSchedule(topic.id, schedule.id, 'down')" title="下移">
                        <i class="el-icon-arrow-down"></i>
                      </el-button>
                    </div>
                    <div class="action-buttons">
                      <el-button type="text" size="small" @click="addOrUpdateHandle(schedule.id)">编辑</el-button>
                      <el-button type="text" size="small" @click="deleteHandle(schedule.id)">删除</el-button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 最后一个插入位置 -->
              <div class="insert-indicator" @click="addScheduleToTopic(topic.id, topic.placeActivityTopicScheduleEntities.length)"
                   :title="getInsertHint(topic, topic.placeActivityTopicScheduleEntities.length)">
                <i class="el-icon-plus"></i>
                <span>在此处插入日程</span>
                <span class="insert-hint">{{ getInsertTimeHint(topic, topic.placeActivityTopicScheduleEntities.length) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <placeactivitytopicschedulediscuss v-if="placeactivitytopicschedulediscussVisible"
      ref="placeactivitytopicschedulediscuss" @refreshDataList="getDataList"></placeactivitytopicschedulediscuss>
    <placeactivitytopicscheduleguest v-if="placeactivitytopicscheduleguestVisible" ref="placeactivitytopicscheduleguest"
      @refreshDataList="getDataList"></placeactivitytopicscheduleguest>
    <placeactivitytopicschedulspeaker v-if="placeactivitytopicschedulspeakerVisible"
      ref="placeactivitytopicschedulspeaker" @refreshDataList="getDataList"></placeactivitytopicschedulspeaker>
    <!-- 选择场地导入弹窗 -->
    <place-import v-if="placeImportVisible" ref="placeImport" @refreshDataList="getDataList"></place-import>
  </div>
</template>

<script>
import AddOrUpdate from './placeactivitytopicschedule-add-or-update'
import placeactivitytopicschedulediscuss from './placeactivitytopicschedulediscuss-guest'
import placeactivitytopicscheduleguest from './placeactivitytopicscheduleguest-guest'
import placeactivitytopicschedulspeaker from './placeactivitytopicschedulspeaker-guest'
import PlaceImport from './placeactivitytopicschedule-place-import'
export default {
  data() {
    return {
      appid: '',
      dataForm: {
        name: '',
        activityId: undefined,
        placeId: undefined,
        topicId: undefined
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      placeList: [],
      placeTopicList: [],
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      placeactivitytopicschedulediscussVisible: false,
      placeactivitytopicscheduleguestVisible: false,
      placeactivitytopicschedulspeakerVisible: false,
      placeImportVisible: false,
      searchTimer: null, // 搜索防抖定时器
    }
  },
  components: {
    AddOrUpdate,
    placeactivitytopicschedulediscuss,
    placeactivitytopicschedulspeaker,
    Upload: () => import('@/components/upload'),
    placeactivitytopicscheduleguest,
    PlaceImport
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.dataForm.activityId = this.$route.query.activityId;
    this.dataForm.placeId = this.$route.query.placeId || undefined;
    this.dataForm.topicId = this.$route.query.topicId || undefined;
    this.getDataList()
    this.getPlace()
    this.getPlaceTopic()
  },
  methods: {
      searchList() {
        this.pageIndex = 1
        this.getDataList()
      },
      // 防抖搜索
      debouncedSearch() {
        if (this.searchTimer) {
          clearTimeout(this.searchTimer)
        }
        this.searchTimer = setTimeout(() => {
          this.searchList()
        }, 500)
      },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/place/placeactivitytopicschedule/listByTopic'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'activityId': this.dataForm.activityId,
          'placeId': this.dataForm.placeId,
          'topicId': this.dataForm.topicId,
          'name': this.dataForm.name
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list || []
          this.totalPage = data.page.totalCount || 0
        } else {
          this.dataList = []
          this.totalPage = 0
          this.$message.error(data.msg || '获取数据失败')
        }
        this.dataListLoading = false
      }).catch(error => {
        console.error('获取数据失败:', error)
        this.dataList = []
        this.totalPage = 0
        this.dataListLoading = false
        this.$message.error('网络错误，请重试')
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId, this.dataForm.topicId, id);
      })
    },
    // 向主题添加日程（支持指定插入位置）
    addScheduleToTopic(topicId, insertPosition) {
      if (!topicId) {
        this.$message.error('主题ID不能为空')
        return
      }

      // 查找对应的主题数据
      const topic = this.dataList.find(t => t.id === topicId)
      if (!topic) {
        this.$message.error('找不到对应的主题')
        return
      }

      // 计算预填充的时间信息
      const timeInfo = this.calculateInsertTime(topic, insertPosition)

      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId, topicId, null, insertPosition, timeInfo);
      })
    },
    // 移动日程位置
    moveSchedule(topicId, scheduleId, direction) {
      this.$confirm(`确定要${direction === 'up' ? '上移' : '下移'}该日程吗？\n\n移动后将自动调整相关日程的时间。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/place/placeactivitytopicschedule/move'),
          method: 'post',
          data: this.$http.adornData({
            scheduleId: scheduleId,
            topicId: topicId,
            direction: direction
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '移动成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg || '移动失败')
          }
        }).catch(error => {
          console.error('移动日程失败:', error)
          this.$message.error('移动失败，请重试')
        })
      }).catch(() => {
        // 用户取消操作
      })
    },
    // 同步主题下所有日程的时间，使其连贯
    syncTopicScheduleTimes(topic) {
      if (!topic.placeActivityTopicScheduleEntities || topic.placeActivityTopicScheduleEntities.length < 2) {
        this.$message.warning('该主题下日程数量不足，无需同步')
        return
      }

      const scheduleCount = topic.placeActivityTopicScheduleEntities.length
      const totalDuration = this.calculateTotalDuration(topic.placeActivityTopicScheduleEntities)

      this.$confirm(`确定要同步该主题下的所有日程时间吗？

主题：${topic.name}
日程数量：${scheduleCount}个
总时长：${Math.round(totalDuration / 60)}分钟

同步后，所有日程将按顺序连贯排列，从主题开始时间开始，每个日程紧接着上一个日程结束。`, '时间同步确认', {
        confirmButtonText: '确定同步',
        cancelButtonText: '取消',
        type: 'info',
        dangerouslyUseHTMLString: true
      }).then(() => {
        this.performTimeSync(topic)
      }).catch(() => {
        // 用户取消操作
      })
    },
    // 执行时间同步
    performTimeSync(topic) {
      this.$http({
        url: this.$http.adornUrl('/place/placeactivitytopicschedule/syncTimes'),
        method: 'post',
        data: this.$http.adornData({
          topicId: topic.id,
          startTime: topic.startTime
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message({
            message: '时间同步成功！所有日程时间已连贯排列',
            type: 'success',
            duration: 2000,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg || '时间同步失败')
        }
      }).catch(error => {
        console.error('时间同步失败:', error)
        this.$message.error('时间同步失败，请重试')
      })
    },
    // 计算所有日程的总时长（分钟）
    calculateTotalDuration(schedules) {
      let totalDuration = 0
      schedules.forEach(schedule => {
        if (schedule.startTime && schedule.endTime) {
          const duration = new Date(schedule.endTime).getTime() - new Date(schedule.startTime).getTime()
          totalDuration += duration
        }
      })
      return totalDuration / (1000 * 60) // 转换为分钟
    },
    // 同步所有主题的时间
    syncAllTopicTimes() {
      if (this.dataList.length === 0) {
        this.$message.warning('没有可同步的主题')
        return
      }

      const topicCount = this.dataList.length
      const totalScheduleCount = this.dataList.reduce((total, topic) => {
        return total + (topic.placeActivityTopicScheduleEntities ? topic.placeActivityTopicScheduleEntities.length : 0)
      }, 0)

      this.$confirm(`确定要同步所有主题的日程时间吗？

主题数量：${topicCount}个
日程总数：${totalScheduleCount}个

同步后，每个主题下的所有日程将按顺序连贯排列，从各自主题的开始时间开始。`, '全部时间同步确认', {
        confirmButtonText: '确定同步',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }).then(() => {
        this.performAllTopicsSync()
      }).catch(() => {
        // 用户取消操作
      })
    },
    // 执行所有主题的时间同步
    performAllTopicsSync() {
      const syncPromises = this.dataList.map(topic => {
        return this.$http({
          url: this.$http.adornUrl('/place/placeactivitytopicschedule/syncTimes'),
          method: 'post',
          data: this.$http.adornData({
            topicId: topic.id,
            startTime: topic.startTime
          })
        })
      })

      Promise.all(syncPromises).then(results => {
        const successCount = results.filter(result => result.data && result.data.code === 200).length
        const failCount = results.length - successCount

        if (failCount === 0) {
          this.$message({
            message: `全部时间同步成功！共同步了 ${successCount} 个主题的日程时间`,
            type: 'success',
            duration: 3000,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message({
            message: `时间同步完成：成功 ${successCount} 个，失败 ${failCount} 个`,
            type: 'warning',
            duration: 3000,
            onClose: () => {
              this.getDataList()
            }
          })
        }
      }).catch(error => {
        console.error('批量时间同步失败:', error)
        this.$message.error('批量时间同步失败，请重试')
      })
    },
    // 格式化主题时间
    formatTopicTime(startTime, endTime) {
      if (!startTime || !endTime) return '-'
      const start = new Date(startTime)
      const end = new Date(endTime)
      const formatTime = (date) => {
        return date.toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
      return `${formatTime(start)} - ${formatTime(end)}`
    },
    // 格式化日程时间
    formatScheduleTime(startTime, endTime) {
      if (!startTime || !endTime) return '-'
      const start = new Date(startTime)
      const end = new Date(endTime)
      const formatTime = (date) => {
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }
      return `${formatTime(start)} - ${formatTime(end)}`
    },
    // 讨论修改
    showDiscuss(id) {
      this.placeactivitytopicschedulediscussVisible = true
      this.$nextTick(() => {
        this.$refs.placeactivitytopicschedulediscuss.init(this.dataForm.activityId, id);
      })
    },
    // 讲者修改
    showGuest(id) {
      this.placeactivitytopicscheduleguestVisible = true
      this.$nextTick(() => {
        this.$refs.placeactivitytopicscheduleguest.init(this.dataForm.activityId, id);
      })
    },
    // 主持修改
    showSpeaker(id) {
      this.placeactivitytopicschedulspeakerVisible = true
      this.$nextTick(() => {
        this.$refs.placeactivitytopicschedulspeaker.init(this.dataForm.activityId, id);
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })

      // 如果是单个删除，询问是否需要自动调整后续日程时间
      let confirmMessage = `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`
      if (id) {
        confirmMessage += '\n\n删除后将自动调整后续日程时间，使时间连续。'
      }

      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/place/placeactivitytopicschedule/delete'),
          method: 'post',
          data: this.$http.adornData({
            ids: ids,
            autoAdjustTime: !!id // 单个删除时启用时间自动调整
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getPlace() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivity/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.placeList = data.result;
        }
      });
    },
    getPlaceTopic() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivitytopic/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.placeTopicList = data.result;
        }
      });
    },
    downloadDemo() {
      var url = this.$http.adornUrl("/place/placeactivitytopicschedule/downloadDemo?" + [
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    exportHandle() {
      var url = this.$http.adornUrl("/place/placeactivitytopicschedule/exportScheduleWord?" + [
        "token=" + this.$cookie.get('token'),
        "activityId=" + this.dataForm.activityId,
        "placeId=" + (this.dataForm.placeId || ''),
        "topicId=" + (this.dataForm.topicId || ''),
        "name=" + (this.dataForm.name || ''),
        "page=1",
        "limit=65535"
      ].join('&'));
      window.open(url);
    },
    exportPageBreakHandle() {
      var url = this.$http.adornUrl("/place/placeactivitytopicschedule/exportScheduleWordWithPageBreak?" + [
        "token=" + this.$cookie.get('token'),
        "activityId=" + this.dataForm.activityId,
        "placeId=" + (this.dataForm.placeId || ''),
        "topicId=" + (this.dataForm.topicId || ''),
        "name=" + (this.dataForm.name || ''),
        "page=1",
        "limit=65535"
      ].join('&'));
      window.open(url);
    },
    exportExcelHandle() {
      var url = this.$http.adornUrl("/place/placeactivitytopicschedule/exportScheduleExcel?" + [
        "token=" + this.$cookie.get('token'),
        "activityId=" + this.dataForm.activityId,
        "placeId=" + (this.dataForm.placeId || ''),
        "topicId=" + (this.dataForm.topicId || ''),
        "name=" + (this.dataForm.name || ''),
        "page=1",
        "limit=65535"
      ].join('&'));
      window.open(url);
    },
    // 选择场地导入
    placeImportHandle() {
      this.placeImportVisible = true
      this.$nextTick(() => {
        this.$refs.placeImport.init(this.dataForm.activityId);
      })
    },
    // 处理下拉菜单命令
    handleDropdownCommand(command) {
      switch (command) {
        case 'placeImport':
          this.placeImportHandle();
          break;
        case 'exportWord':
          this.exportHandle();
          break;
        case 'exportWordPageBreak':
          this.exportPageBreakHandle();
          break;
        case 'exportExcel':
          this.exportExcelHandle();
          break;
        default:
          break;
      }
    },
    // 计算时长（分钟）
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) {
        return '-'
      }
      try {
        const start = new Date(startTime)
        const end = new Date(endTime)
        const durationMs = end.getTime() - start.getTime()
        const durationMinutes = Math.round(durationMs / (1000 * 60))
        return durationMinutes > 0 ? durationMinutes : '-'
      } catch (error) {
        return '-'
      }
    },
    // 获取显示名称（优先显示别名）
    getDisplayName(originalName, aliasNames, participantId) {
      // 如果没有别名，直接返回原名
      if (!aliasNames) {
        return originalName
      }

      try {
        // 解析别名JSON字符串
        const aliases = typeof aliasNames === 'string' ? JSON.parse(aliasNames) : aliasNames

        // 调试信息（开发环境）
        if (process.env.NODE_ENV === 'development') {
          console.log('别名解析:', {
            originalName,
            aliasNames,
            participantId,
            parsedAliases: aliases
          })
        }

        // 如果别名是数组，查找对应ID的别名
        if (Array.isArray(aliases)) {
          const aliasItem = aliases.find(item =>
            item.id === participantId ||
            item.guestId === participantId ||
            item.activityGuestId === participantId
          )
          if (aliasItem && aliasItem.name) {
            return aliasItem.name
          }
        }

        // 如果别名是对象，直接查找
        if (typeof aliases === 'object' && aliases[participantId]) {
          return aliases[participantId]
        }

        // 如果别名是字符串且只有一个参与者，直接使用
        if (typeof aliases === 'string' && aliases.trim()) {
          return aliases
        }

      } catch (error) {
        console.warn('解析别名失败:', error, {
          originalName,
          aliasNames,
          participantId
        })
      }

      // 解析失败或没有找到别名，返回原名
      return originalName
    },
    // 获取标签名称（如果有别名配置则显示别名，否则显示默认名称）
    getLabelName(defaultLabel, aliasNames) {
      // 如果没有别名配置，返回默认标签
      if (!aliasNames || aliasNames.trim() === '') {
        return defaultLabel
      }

      // 直接返回别名作为标签名称
      // aliasGuestName, aliasSpeakerName, aliasDiscussName 字段存储的是标签的自定义名称
      return aliasNames.trim()
    },
    // 计算插入位置的时间信息
    calculateInsertTime(topic, insertPosition) {
      const schedules = topic.placeActivityTopicScheduleEntities || []

      // 如果没有现有日程，使用主题的开始时间
      if (schedules.length === 0) {
        return {
          startTime: topic.startTime,
          endTime: null,
          duration: 60 // 默认60分钟
        }
      }

      // 如果插入到开头（位置0）
      if (insertPosition === 0) {
        const firstSchedule = schedules[0]
        if (firstSchedule && firstSchedule.startTime) {
          // 使用主题开始时间到第一个日程开始时间之间
          return {
            startTime: topic.startTime,
            endTime: firstSchedule.startTime,
            duration: this.calculateDuration(topic.startTime, firstSchedule.startTime)
          }
        }
        return {
          startTime: topic.startTime,
          endTime: null,
          duration: 60
        }
      }

      // 如果插入到末尾
      if (insertPosition >= schedules.length) {
        const lastSchedule = schedules[schedules.length - 1]
        if (lastSchedule && lastSchedule.endTime) {
          // 从最后一个日程的结束时间开始
          const startTime = lastSchedule.endTime
          const endTime = new Date(new Date(startTime).getTime() + 60 * 60 * 1000) // 默认1小时后
          return {
            startTime: startTime,
            endTime: endTime,
            duration: 60
          }
        }
        return {
          startTime: null,
          endTime: null,
          duration: 60
        }
      }

      // 插入到中间位置
      const prevSchedule = schedules[insertPosition - 1]
      const nextSchedule = schedules[insertPosition]

      if (prevSchedule && nextSchedule && prevSchedule.endTime && nextSchedule.startTime) {
        // 使用前一个日程的结束时间到后一个日程的开始时间
        return {
          startTime: prevSchedule.endTime,
          endTime: nextSchedule.startTime,
          duration: this.calculateDuration(prevSchedule.endTime, nextSchedule.startTime)
        }
      } else if (prevSchedule && prevSchedule.endTime) {
        // 只有前一个日程，从其结束时间开始
        const startTime = prevSchedule.endTime
        const endTime = new Date(new Date(startTime).getTime() + 60 * 60 * 1000) // 默认1小时后
        return {
          startTime: startTime,
          endTime: endTime,
          duration: 60
        }
      } else if (nextSchedule && nextSchedule.startTime) {
        // 只有后一个日程，计算到其开始时间
        const endTime = nextSchedule.startTime
        const startTime = new Date(new Date(endTime).getTime() - 60 * 60 * 1000) // 默认1小时前
        return {
          startTime: startTime,
          endTime: endTime,
          duration: 60
        }
      }

      // 兜底情况
      return {
        startTime: null,
        endTime: null,
        duration: 60
      }
    },
    // 获取插入提示信息
    getInsertHint(topic, insertPosition) {
      const timeInfo = this.calculateInsertTime(topic, insertPosition)
      if (timeInfo.startTime && timeInfo.endTime) {
        return `将自动填充时间：${this.formatScheduleTime(timeInfo.startTime, timeInfo.endTime)}`
      } else if (timeInfo.startTime) {
        return `将自动填充开始时间：${this.formatTime(timeInfo.startTime)}`
      } else {
        return '点击插入新日程'
      }
    },
    // 获取插入时间简短提示
    getInsertTimeHint(topic, insertPosition) {
      const timeInfo = this.calculateInsertTime(topic, insertPosition)
      if (timeInfo.startTime && timeInfo.endTime) {
        return `(${this.formatTime(timeInfo.startTime)}-${this.formatTime(timeInfo.endTime)})`
      } else if (timeInfo.startTime) {
        return `(${this.formatTime(timeInfo.startTime)}起)`
      } else {
        return ''
      }
    },
    // 格式化时间（只显示时分）
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },
  }
}
</script>

<style scoped>
.topic-schedule-container {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.topic-group {
  margin-bottom: 30px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  overflow: hidden;
}

.topic-header {
  background: #f8f9fa;
  padding: 16px 20px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 60px; /* 确保有足够的高度 */
}

.topic-info {
  flex: 1;
}

.topic-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.topic-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.place-name {
  color: #409eff;
  font-weight: 500;
}

.topic-time {
  color: #67c23a;
}

.schedule-count {
  color: #909399;
}

.topic-actions {
  flex-shrink: 0;
  display: flex;
  gap: 8px;
  align-items: center;
  height: 100%; /* 确保占满父容器高度 */
}

.topic-actions .el-button {
  height: 32px; /* 统一按钮高度 */
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.schedules-container {
  padding: 16px 20px;
}

.no-schedules {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.schedule-list {
  position: relative;
}

.insert-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  margin: 8px 0;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  color: #909399;
  font-size: 14px;
  transition: all 0.3s;
  opacity: 0.6;
  position: relative;
}

.insert-indicator:hover {
  border-color: #409eff;
  color: #409eff;
  background: #f0f9ff;
  opacity: 1;
}

.insert-indicator i {
  margin-right: 8px;
}

.insert-hint {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.insert-indicator:hover .insert-hint {
  color: #409eff;
}

.schedule-card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 16px;
  margin: 6px 0;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  background: #fff;
  transition: all 0.3s;
}

.schedule-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.schedule-content {
  flex: 1;
}

.schedule-header {
  margin-bottom: 8px;
}

.schedule-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.schedule-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.schedule-time {
  font-size: 13px;
  color: #67c23a;
  font-weight: 500;
}

.schedule-duration {
  font-size: 12px;
  color: #909399;
}

.schedule-participants {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.participant-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  min-height: 24px;
}

.participant-label {
  font-size: 12px;
  color: #666;
  min-width: 36px;
  line-height: 22px;
  flex-shrink: 0;
}

.participant-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
}

.participant-tags .el-tag {
  cursor: pointer;
  margin: 0;
}

.schedule-actions {
  flex-shrink: 0;
  display: flex;
  gap: 8px;
  margin-left: 16px;
}

.move-buttons {
  display: flex;
  gap: 2px;
  align-items: center;
}

.move-buttons .el-button {
  padding: 2px 4px;
  min-width: 24px;
  height: 24px;
}

.move-buttons .el-button i {
  font-size: 12px;
}

.move-buttons .el-button:disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

/* 确认状态样式 */
.tag-color-0 {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
  color: #fff !important;
}

.tag-color-1 {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
  color: #fff !important;
}

.tag-color-2 {
  background-color: #e6a23c !important;
  border-color: #e6a23c !important;
  color: #fff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .topic-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .topic-meta {
    flex-direction: column;
    gap: 8px;
  }

  .schedule-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .participant-group {
    flex-wrap: wrap;
  }
}
</style>
